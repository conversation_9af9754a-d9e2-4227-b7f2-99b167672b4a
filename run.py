"""
启动脚本
检查环境并启动主程序
"""

import sys
import os
import subprocess
import traceback

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        "cv2", "numpy", "torch", "PIL", "pyautogui", 
        "pynput", "win32gui", "ultralytics"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "cv2":
                import cv2
            elif package == "PIL":
                from PIL import Image
            elif package == "win32gui":
                import win32gui
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    return missing_packages

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "main.py",
        "modules/__init__.py",
        "modules/screen_capture.py",
        "modules/game_controller.py",
        "modules/item_detector.py",
        "modules/pathfinding.py",
        "modules/ai_trainer.py",
        "modules/config_manager.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path} 不存在")
    
    return missing_files

def create_missing_directories():
    """创建缺失的目录"""
    directories = [
        "models",
        "data", 
        "screenshots",
        "training_data",
        "training_data/images",
        "training_data/labels"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✓ 创建目录: {directory}")

def main():
    """主启动流程"""
    print("=" * 50)
    print("燕云十六声自动寻路采集程序")
    print("=" * 50)
    
    print("检查Python版本...")
    if sys.version_info < (3, 8):
        print(f"✗ Python版本过低: {sys.version}")
        print("需要Python 3.8或更高版本")
        input("按Enter键退出...")
        return
    print(f"✓ Python版本: {sys.version}")
    
    print("\n检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行 python install.py 安装依赖")
        input("按Enter键退出...")
        return
    
    print("\n检查程序文件...")
    missing_files = check_files()
    
    if missing_files:
        print(f"\n缺少以下文件: {', '.join(missing_files)}")
        print("请确保所有程序文件完整")
        input("按Enter键退出...")
        return
    
    print("\n检查目录结构...")
    create_missing_directories()
    
    print("\n" + "=" * 50)
    print("环境检查完成，启动主程序...")
    print("=" * 50)
    print("\n使用提示:")
    print("1. 程序启动后，请先设置游戏窗口")
    print("2. 可以使用'窗口选择工具'来快速选择游戏窗口")
    print("3. 确保游戏在运行状态")
    print("4. 建议先使用'开始预览'测试窗口捕获效果")
    print("=" * 50)

    try:
        # 启动主程序
        from main import YanYunAutoBot

        app = YanYunAutoBot()
        app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
