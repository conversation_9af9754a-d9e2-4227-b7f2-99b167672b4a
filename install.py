"""
安装脚本
自动安装所需的依赖包
"""

import subprocess
import sys
import os
import pkg_resources
from typing import List

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"Python版本检查通过: {sys.version}")
    return True

def install_package(package: str):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False

def check_package_installed(package: str) -> bool:
    """检查包是否已安装"""
    try:
        pkg_resources.get_distribution(package)
        return True
    except pkg_resources.DistributionNotFound:
        return False

def install_requirements():
    """安装requirements.txt中的依赖"""
    if not os.path.exists("requirements.txt"):
        print("错误: 找不到requirements.txt文件")
        return False
    
    print("开始安装依赖包...")
    
    # 读取requirements.txt
    with open("requirements.txt", "r", encoding="utf-8") as f:
        packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]
    
    failed_packages = []
    
    for package in packages:
        # 提取包名（去掉版本号）
        package_name = package.split("==")[0].split(">=")[0].split("<=")[0]
        
        # 跳过内置模块
        if package_name in ["tkinter", "threading", "time", "json", "os", "math", "random", "datetime", "pathlib", "collections", "logging"]:
            continue
        
        if not check_package_installed(package_name):
            if not install_package(package):
                failed_packages.append(package)
        else:
            print(f"✓ {package_name} 已安装")
    
    if failed_packages:
        print(f"\n以下包安装失败:")
        for pkg in failed_packages:
            print(f"  - {pkg}")
        return False
    
    print("\n所有依赖包安装完成!")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "models",
        "data", 
        "screenshots",
        "training_data",
        "training_data/images",
        "training_data/labels"
    ]
    
    print("创建必要目录...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ 创建目录: {directory}")

def download_models():
    """下载预训练模型"""
    print("准备下载预训练模型...")
    
    try:
        # 这里可以添加下载YOLOv8模型的代码
        from ultralytics import YOLO
        
        print("下载YOLOv8模型...")
        model = YOLO('yolov8n.pt')  # 这会自动下载模型
        print("✓ YOLOv8模型下载完成")
        
        return True
    except Exception as e:
        print(f"✗ 模型下载失败: {e}")
        print("可以稍后在程序中手动下载")
        return False

def create_config_file():
    """创建默认配置文件"""
    if not os.path.exists("config.json"):
        print("创建默认配置文件...")
        
        default_config = {
            "game": {
                "window_title": "燕云十六声",
                "capture_fps": 10,
                "movement_speed": 1.0,
                "turn_speed": 1.0
            },
            "detection": {
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "max_detection_distance": 100.0,
                "model_path": "models/item_detector.pt"
            },
            "pathfinding": {
                "grid_size": 1.0,
                "precision_threshold": 5.0,
                "max_iterations": 10000,
                "movement_costs": {
                    "horizontal": 1.0,
                    "vertical": 1.4,
                    "climb": 2.0
                }
            },
            "ui": {
                "window_width": 800,
                "window_height": 600,
                "theme": "default"
            },
            "hotkeys": {
                "start_stop": "F1",
                "emergency_stop": "F2",
                "manual_collect": "F3"
            }
        }
        
        import json
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print("✓ 配置文件创建完成")

def main():
    """主安装流程"""
    print("=" * 50)
    print("燕云十六声自动寻路采集程序 - 安装向导")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按Enter键退出...")
        return
    
    # 升级pip
    print("\n升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip升级完成")
    except:
        print("✗ pip升级失败，继续安装...")
    
    # 安装依赖
    print("\n" + "=" * 30)
    if not install_requirements():
        print("依赖安装失败，请手动安装")
        input("按Enter键退出...")
        return
    
    # 创建目录
    print("\n" + "=" * 30)
    create_directories()
    
    # 创建配置文件
    print("\n" + "=" * 30)
    create_config_file()
    
    # 下载模型
    print("\n" + "=" * 30)
    download_models()
    
    print("\n" + "=" * 50)
    print("安装完成!")
    print("=" * 50)
    print("使用说明:")
    print("1. 运行 python main.py 启动主程序")
    print("2. 首次使用建议先打开AI训练工具进行数据标注")
    print("3. 确保游戏窗口标题包含'燕云十六声'")
    print("4. 建议在游戏中测试各项功能")
    print("=" * 50)
    
    input("按Enter键退出...")

if __name__ == "__main__":
    main()
