"""
窗口选择工具
帮助用户选择游戏窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import win32gui
import win32api
import win32con
from modules.screen_capture import ScreenCapture
import cv2
from PIL import Image, ImageTk

class WindowSelector:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏窗口选择工具")
        self.root.geometry("800x600")
        
        self.screen_capture = ScreenCapture()
        self.selected_hwnd = None
        self.window_data = {}
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="游戏窗口选择工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_label = ttk.Label(main_frame, 
                              text="请从下面的列表中选择游戏窗口，然后点击'设置为游戏窗口'")
        info_label.pack(pady=(0, 10))
        
        # 窗口列表框架
        list_frame = ttk.LabelFrame(main_frame, text="可用窗口列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 窗口列表
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.window_listbox = tk.Listbox(list_container, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, 
                                 command=self.window_listbox.yview)
        self.window_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.window_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.window_listbox.bind("<<ListboxSelect>>", self.on_window_select)
        self.window_listbox.bind("<Double-Button-1>", self.on_window_double_click)
        
        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="窗口预览", padding="10")
        preview_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.preview_canvas = tk.Canvas(preview_frame, width=400, height=300, bg="gray")
        self.preview_canvas.pack()
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="刷新窗口列表", 
                  command=self.refresh_windows).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="预览选中窗口", 
                  command=self.preview_selected_window).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="设置为游戏窗口", 
                  command=self.set_game_window).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="取消", 
                  command=self.root.destroy).pack(side=tk.RIGHT)
    
    def refresh_windows(self):
        """刷新窗口列表"""
        try:
            windows = self.screen_capture.get_all_windows()
            
            self.window_listbox.delete(0, tk.END)
            self.window_data.clear()
            
            for hwnd, title in windows:
                # 过滤掉一些系统窗口
                if self.is_valid_game_window(hwnd, title):
                    display_text = f"[{hwnd:8}] {title}"
                    self.window_listbox.insert(tk.END, display_text)
                    self.window_data[display_text] = (hwnd, title)
            
            print(f"找到 {len(self.window_data)} 个可用窗口")
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新窗口列表失败: {str(e)}")
    
    def is_valid_game_window(self, hwnd, title):
        """判断是否是有效的游戏窗口"""
        if not title.strip():
            return False
        
        # 过滤掉一些明显的系统窗口
        system_windows = [
            "Program Manager", "Desktop", "Taskbar", "Start",
            "Windows Input Experience", "Microsoft Text Input Application",
            "Settings", "Task Manager", "File Explorer"
        ]
        
        for sys_win in system_windows:
            if sys_win.lower() in title.lower():
                return False
        
        # 检查窗口是否有足够的大小
        try:
            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # 过滤掉太小的窗口
            if width < 200 or height < 150:
                return False
                
        except:
            return False
        
        return True
    
    def on_window_select(self, event):
        """窗口选择事件"""
        selection = self.window_listbox.curselection()
        if selection:
            selected_text = self.window_listbox.get(selection[0])
            if selected_text in self.window_data:
                self.selected_hwnd, _ = self.window_data[selected_text]
    
    def on_window_double_click(self, event):
        """双击窗口事件"""
        self.preview_selected_window()
    
    def preview_selected_window(self):
        """预览选中的窗口"""
        if not self.selected_hwnd:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        try:
            # 临时设置窗口并捕获
            old_hwnd = self.screen_capture.game_hwnd
            self.screen_capture.game_hwnd = self.selected_hwnd
            
            screenshot = self.screen_capture.capture_window(self.selected_hwnd)
            
            # 恢复原来的设置
            self.screen_capture.game_hwnd = old_hwnd
            
            if screenshot is not None:
                self.display_preview(screenshot)
            else:
                messagebox.showerror("错误", "无法捕获窗口画面")
                
        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {str(e)}")
    
    def display_preview(self, image):
        """显示预览图像"""
        try:
            # 缩放图像以适应预览区域
            height, width = image.shape[:2]
            canvas_width = 400
            canvas_height = 300
            
            scale_x = canvas_width / width
            scale_y = canvas_height / height
            scale = min(scale_x, scale_y)
            
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            resized_image = cv2.resize(image, (new_width, new_height))
            rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
            
            # 转换为PhotoImage
            pil_image = Image.fromarray(rgb_image)
            photo = ImageTk.PhotoImage(pil_image)
            
            # 显示在画布上
            self.preview_canvas.delete("all")
            x = (canvas_width - new_width) // 2
            y = (canvas_height - new_height) // 2
            self.preview_canvas.create_image(x, y, anchor=tk.NW, image=photo)
            
            # 保持引用
            self.preview_canvas.image = photo
            
        except Exception as e:
            print(f"显示预览失败: {e}")
    
    def set_game_window(self):
        """设置游戏窗口"""
        if not self.selected_hwnd:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        try:
            if self.screen_capture.set_game_window_by_hwnd(self.selected_hwnd):
                selected_text = None
                for text, (hwnd, title) in self.window_data.items():
                    if hwnd == self.selected_hwnd:
                        selected_text = title
                        break
                
                messagebox.showinfo("成功", f"游戏窗口设置成功!\n窗口: {selected_text}")
                
                # 保存设置到配置文件
                try:
                    from modules.config_manager import ConfigManager
                    config = ConfigManager()
                    config.set("game.window_title", selected_text)
                    config.save_config()
                except:
                    pass
                
                self.root.destroy()
            else:
                messagebox.showerror("错误", "设置游戏窗口失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"设置失败: {str(e)}")
    
    def run(self):
        """运行窗口选择器"""
        self.root.mainloop()
        return self.screen_capture.game_hwnd

def main():
    """主函数"""
    selector = WindowSelector()
    return selector.run()

if __name__ == "__main__":
    selected_hwnd = main()
    if selected_hwnd:
        print(f"选择的窗口句柄: {selected_hwnd}")
    else:
        print("未选择窗口")
