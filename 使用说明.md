# 燕云十六声自动寻路采集程序 - 使用说明

## 🚀 快速开始

### 第一步：安装程序
1. 双击运行 `安装依赖.bat` 或执行 `python install.py`
2. 等待所有依赖包安装完成

### 第二步：启动程序
1. 双击运行 `启动程序.bat` 或执行 `python run.py`
2. 程序将自动检查环境并启动主界面

### 第三步：设置游戏窗口
这是最重要的一步！程序需要知道哪个是游戏窗口才能正常工作。

#### 方法一：使用窗口选择工具（推荐）
1. 启动游戏并进入游戏世界
2. 在程序中点击 **"窗口选择工具"** 按钮
3. 在弹出的窗口中选择游戏窗口
4. 点击 **"预览选中窗口"** 确认是否正确
5. 点击 **"设置为游戏窗口"** 完成设置

#### 方法二：使用下拉列表
1. 点击 **"刷新窗口列表"** 按钮
2. 从下拉列表中选择游戏窗口
3. 点击 **"设置为游戏窗口"**

#### 方法三：独立窗口选择工具
1. 双击运行 `选择游戏窗口.bat`
2. 在独立工具中选择游戏窗口

## 🎮 基本功能使用

### 实时预览
1. 设置好游戏窗口后，点击 **"开始预览"**
2. 程序会实时显示游戏画面和检测结果
3. 在预览窗口中可以看到识别到的物品

### 截图功能
1. 点击 **"截图"** 按钮
2. 截图会保存到 `screenshots` 目录
3. 可用于收集训练数据

### 手动寻路
1. 在目标坐标框中输入 X、Y、Z 坐标
2. 点击 **"设置目标"**
3. 点击 **"手动寻路"** 开始移动

### 自动采集
1. 确保游戏窗口设置正确
2. 点击 **"开始自动采集"**
3. 程序会自动检测物品并移动采集
4. 点击 **"停止采集"** 结束

## 🧠 AI训练使用

### 收集训练数据
1. 在游戏中找到各种可采集物品
2. 使用截图功能保存包含物品的图像
3. 收集不同角度、光照的样本

### 数据标注
1. 点击 **"AI训练工具"** 按钮
2. 点击 **"打开标注工具"**
3. 加载要标注的图像
4. 用鼠标框选物品位置
5. 选择正确的物品类别
6. 保存标注数据

### 模型训练
1. 在AI训练工具中设置训练参数
2. 点击 **"开始训练"**
3. 等待训练完成
4. 测试训练好的模型

## ⚙️ 参数设置

### 游戏设置
- **移动速度**：控制角色移动的速度（0.1-3.0）
- **转向速度**：控制角色转向的速度（0.1-3.0）

### 检测设置
- **置信度阈值**：AI检测的最低置信度（0.1-1.0）
- 数值越高，检测越严格但可能漏检
- 数值越低，检测越宽松但可能误检

## 🔧 故障排除

### 问题1：找不到游戏窗口
**解决方法**：
1. 确保游戏正在运行
2. 使用"窗口选择工具"手动选择
3. 检查游戏是否最小化
4. 尝试刷新窗口列表

### 问题2：预览显示黑屏
**解决方法**：
1. 重新设置游戏窗口
2. 确保游戏在前台显示
3. 检查游戏是否被其他窗口遮挡

### 问题3：物品识别不准确
**解决方法**：
1. 收集更多训练数据
2. 重新训练AI模型
3. 调整置信度阈值
4. 确保游戏画面清晰

### 问题4：自动移动不正常
**解决方法**：
1. 调整移动速度参数
2. 检查游戏控制设置
3. 确保游戏窗口处于活动状态

### 问题5：程序运行缓慢
**解决方法**：
1. 关闭不必要的预览功能
2. 降低捕获帧率
3. 使用独立显卡
4. 关闭其他占用资源的程序

## 📋 使用技巧

### 提高识别准确率
1. **收集多样化数据**：不同时间、天气、角度的物品图像
2. **精确标注**：确保标注框准确包围物品
3. **平衡数据**：每种物品类别的样本数量要相近
4. **质量优于数量**：少量高质量数据比大量低质量数据更有效

### 优化寻路效果
1. **合理设置目标**：避免设置无法到达的位置
2. **清理障碍物数据**：定期更新地图障碍物信息
3. **调整网格大小**：根据游戏环境调整寻路精度

### 安全使用建议
1. **测试环境**：建议先在测试服务器使用
2. **适度使用**：避免长时间连续运行
3. **人工监控**：定期检查程序运行状态
4. **遵守规则**：确保符合游戏服务条款

## 🎯 最佳实践

### 首次使用流程
1. 安装程序 → 启动程序 → 设置游戏窗口
2. 开始预览 → 测试截图 → 收集训练数据
3. 标注数据 → 训练模型 → 测试效果
4. 调整参数 → 开始自动采集

### 日常使用流程
1. 启动游戏 → 启动程序 → 确认窗口设置
2. 开始预览 → 检查检测效果 → 开始自动采集
3. 监控运行状态 → 适时停止 → 检查采集结果

## ⚠️ 重要提醒

1. **合规使用**：本程序仅供学习研究，请遵守游戏服务条款
2. **风险提示**：使用自动化工具可能存在封号风险
3. **备份数据**：定期备份训练数据和模型文件
4. **版本更新**：关注程序更新，及时升级功能

---

如有问题，请查看 `README.md` 获取更详细的技术文档。
