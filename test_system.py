"""
系统测试脚本
测试各个模块的功能
"""

import sys
import os
import time
import traceback
import cv2
import numpy as np

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from modules.screen_capture import ScreenCapture
        from modules.game_controller import GameController
        from modules.item_detector import ItemDetector
        from modules.pathfinding import PathFinder
        from modules.ai_trainer import AITrainer
        from modules.config_manager import ConfigManager
        print("✓ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        traceback.print_exc()
        return False

def test_screen_capture():
    """测试屏幕捕获功能"""
    print("\n测试屏幕捕获...")
    
    try:
        from modules.screen_capture import ScreenCapture
        
        capture = ScreenCapture()
        
        # 测试全屏捕获
        screenshot = capture.capture_full_screen()
        if screenshot is not None:
            print("✓ 全屏捕获成功")
            
            # 保存测试截图
            cv2.imwrite("test_screenshot.png", screenshot)
            print("✓ 测试截图已保存")
            
            return True
        else:
            print("✗ 全屏捕获失败")
            return False
            
    except Exception as e:
        print(f"✗ 屏幕捕获测试失败: {e}")
        return False

def test_game_controller():
    """测试游戏控制功能"""
    print("\n测试游戏控制...")
    
    try:
        from modules.game_controller import GameController
        
        controller = GameController()
        
        # 测试距离计算
        pos1 = {"x": 0, "y": 0, "z": 0}
        pos2 = {"x": 3, "y": 4, "z": 0}
        distance = controller.calculate_distance(pos1, pos2)
        
        if abs(distance - 5.0) < 0.1:
            print("✓ 距离计算正确")
        else:
            print(f"✗ 距离计算错误: 期望5.0，得到{distance}")
            return False
        
        # 测试方向计算
        angle = controller.calculate_direction(pos1, pos2)
        expected_angle = 53.13  # arctan(4/3) in degrees
        
        if abs(angle - expected_angle) < 1.0:
            print("✓ 方向计算正确")
        else:
            print(f"✗ 方向计算错误: 期望{expected_angle}，得到{angle}")
            return False
        
        print("✓ 游戏控制测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 游戏控制测试失败: {e}")
        return False

def test_item_detector():
    """测试物品检测功能"""
    print("\n测试物品检测...")
    
    try:
        from modules.item_detector import ItemDetector
        
        detector = ItemDetector()
        
        # 创建测试图像
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加一些颜色区域模拟物品
        cv2.rectangle(test_image, (100, 100), (200, 200), (0, 255, 0), -1)  # 绿色区域
        cv2.rectangle(test_image, (300, 300), (400, 400), (255, 0, 0), -1)  # 蓝色区域
        
        # 测试检测
        items = detector.detect_items(test_image)
        
        if len(items) > 0:
            print(f"✓ 检测到 {len(items)} 个物品")
            
            # 测试绘制检测结果
            result_image = detector.draw_detections(test_image, items)
            cv2.imwrite("test_detection.png", result_image)
            print("✓ 检测结果图像已保存")
            
            return True
        else:
            print("✗ 未检测到物品（可能是正常的）")
            return True  # 这可能是正常的，因为是简单的测试图像
            
    except Exception as e:
        print(f"✗ 物品检测测试失败: {e}")
        return False

def test_pathfinding():
    """测试寻路功能"""
    print("\n测试寻路算法...")
    
    try:
        from modules.pathfinding import PathFinder
        
        pathfinder = PathFinder()
        
        # 测试简单寻路
        start = {"x": 0, "y": 0, "z": 0}
        goal = {"x": 5, "y": 5, "z": 0}
        
        path = pathfinder.find_path(start, goal)
        
        if path and len(path) > 0:
            print(f"✓ 找到路径，包含 {len(path)} 个路径点")
            
            # 测试路径平滑
            smoothed_path = pathfinder.smooth_path(path)
            print(f"✓ 路径平滑完成，优化为 {len(smoothed_path)} 个路径点")
            
            return True
        else:
            print("✗ 未找到路径")
            return False
            
    except Exception as e:
        print(f"✗ 寻路测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理"""
    print("\n测试配置管理...")
    
    try:
        from modules.config_manager import ConfigManager
        
        config = ConfigManager("test_config.json")
        
        # 测试设置和获取配置
        config.set("test.value", 123)
        value = config.get("test.value")
        
        if value == 123:
            print("✓ 配置设置和获取正确")
        else:
            print(f"✗ 配置错误: 期望123，得到{value}")
            return False
        
        # 测试保存和加载
        config.save_config()
        
        new_config = ConfigManager("test_config.json")
        new_value = new_config.get("test.value")
        
        if new_value == 123:
            print("✓ 配置保存和加载正确")
        else:
            print(f"✗ 配置保存/加载错误: 期望123，得到{new_value}")
            return False
        
        # 清理测试文件
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False

def test_ai_trainer():
    """测试AI训练模块"""
    print("\n测试AI训练模块...")
    
    try:
        from modules.ai_trainer import AITrainer, AnnotationTool
        
        # 测试AI训练器初始化
        trainer = AITrainer()
        print("✓ AI训练器初始化成功")
        
        # 测试标注工具初始化
        annotation_tool = AnnotationTool()
        print("✓ 标注工具初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ AI训练模块测试失败: {e}")
        return False

def test_main_program():
    """测试主程序"""
    print("\n测试主程序...")
    
    try:
        # 不实际启动GUI，只测试导入和初始化
        import main
        print("✓ 主程序导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 主程序测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("燕云十六声自动寻路采集程序 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("屏幕捕获", test_screen_capture),
        ("游戏控制", test_game_controller),
        ("物品检测", test_item_detector),
        ("寻路算法", test_pathfinding),
        ("配置管理", test_config_manager),
        ("AI训练", test_ai_trainer),
        ("主程序", test_main_program)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！系统运行正常")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    print("=" * 50)
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    
    print("\n清理测试文件...")
    test_files = ["test_screenshot.png", "test_detection.png", "test_config.json"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✓ 删除 {file}")
    
    input("\n按Enter键退出...")
    sys.exit(0 if success else 1)
