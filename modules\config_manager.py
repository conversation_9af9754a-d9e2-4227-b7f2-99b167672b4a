"""
配置管理模块
管理程序的各种配置参数
"""

import json
import os
from typing import Dict, Any

class ConfigManager:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_default_config()
        self.load_config()
    
    def load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            "game": {
                "window_title": "燕云十六声",
                "capture_fps": 10,
                "movement_speed": 1.0,
                "turn_speed": 1.0
            },
            "detection": {
                "confidence_threshold": 0.5,
                "nms_threshold": 0.4,
                "max_detection_distance": 100.0,
                "model_path": "models/item_detector.pt"
            },
            "pathfinding": {
                "grid_size": 1.0,
                "precision_threshold": 5.0,
                "max_iterations": 10000,
                "movement_costs": {
                    "horizontal": 1.0,
                    "vertical": 1.4,
                    "climb": 2.0
                }
            },
            "ui": {
                "window_width": 800,
                "window_height": 600,
                "theme": "default"
            },
            "hotkeys": {
                "start_stop": "F1",
                "emergency_stop": "F2",
                "manual_collect": "F3"
            }
        }
    
    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.merge_config(loaded_config)
                print(f"配置已加载: {self.config_file}")
            else:
                print("配置文件不存在，使用默认配置")
                self.save_config()
        except Exception as e:
            print(f"加载配置失败: {e}")
    
    def merge_config(self, new_config: Dict[str, Any]):
        """合并配置（保留默认值）"""
        def merge_dict(default: Dict, new: Dict):
            for key, value in new.items():
                if key in default:
                    if isinstance(default[key], dict) and isinstance(value, dict):
                        merge_dict(default[key], value)
                    else:
                        default[key] = value
        
        merge_dict(self.config, new_config)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"配置已保存: {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值（支持点分隔的路径）"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """设置配置值（支持点分隔的路径）"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_game_config(self) -> Dict[str, Any]:
        """获取游戏相关配置"""
        return self.config.get("game", {})
    
    def get_detection_config(self) -> Dict[str, Any]:
        """获取检测相关配置"""
        return self.config.get("detection", {})
    
    def get_pathfinding_config(self) -> Dict[str, Any]:
        """获取寻路相关配置"""
        return self.config.get("pathfinding", {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI相关配置"""
        return self.config.get("ui", {})
    
    def get_hotkeys_config(self) -> Dict[str, Any]:
        """获取热键配置"""
        return self.config.get("hotkeys", {})
