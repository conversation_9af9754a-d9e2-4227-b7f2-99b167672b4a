"""
窗口捕获测试工具
用于诊断窗口捕获问题
"""

import tkinter as tk
from tkinter import ttk, messagebox
import win32gui
import win32api
import cv2
import numpy as np
from modules.screen_capture import ScreenCapture
from PIL import Image, ImageTk
import os
import time

class CaptureTestTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("窗口捕获测试工具")
        self.root.geometry("900x700")
        
        self.screen_capture = ScreenCapture()
        self.selected_hwnd = None
        self.window_data = {}
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="窗口捕获测试工具", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 窗口选择区域
        select_frame = ttk.LabelFrame(main_frame, text="选择窗口", padding="10")
        select_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 窗口列表
        list_frame = ttk.Frame(select_frame)
        list_frame.pack(fill=tk.X)
        
        ttk.Label(list_frame, text="窗口列表:").pack(side=tk.LEFT)
        
        self.window_var = tk.StringVar()
        self.window_combo = ttk.Combobox(list_frame, textvariable=self.window_var, 
                                        width=50, state="readonly")
        self.window_combo.pack(side=tk.LEFT, padx=(10, 10))
        self.window_combo.bind("<<ComboboxSelected>>", self.on_window_select)
        
        ttk.Button(list_frame, text="刷新", command=self.refresh_windows).pack(side=tk.LEFT)
        
        # 窗口信息显示
        info_frame = ttk.LabelFrame(main_frame, text="窗口信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_text = tk.Text(info_frame, height=6, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 测试按钮区域
        test_frame = ttk.LabelFrame(main_frame, text="测试操作", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(test_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="测试Win32捕获", 
                  command=self.test_win32_capture).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="测试PyAutoGUI捕获", 
                  command=self.test_pyautogui_capture).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="测试全屏捕获", 
                  command=self.test_fullscreen_capture).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="保存截图", 
                  command=self.save_screenshot).pack(side=tk.LEFT)
        
        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="捕获预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)
        
        self.preview_canvas = tk.Canvas(preview_frame, bg="gray")
        self.preview_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="请选择窗口进行测试")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.current_image = None
    
    def refresh_windows(self):
        """刷新窗口列表"""
        try:
            windows = self.screen_capture.get_all_windows()
            window_list = []
            self.window_data = {}
            
            for hwnd, title in windows:
                if title.strip():  # 只显示有标题的窗口
                    display_text = f"[{hwnd}] {title}"
                    window_list.append(display_text)
                    self.window_data[display_text] = (hwnd, title)
            
            self.window_combo['values'] = window_list
            if window_list:
                self.window_combo.current(0)
                self.on_window_select(None)
            
            self.status_var.set(f"找到 {len(window_list)} 个窗口")
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新窗口列表失败: {str(e)}")
    
    def on_window_select(self, event):
        """窗口选择事件"""
        selected = self.window_var.get()
        if selected and selected in self.window_data:
            self.selected_hwnd, title = self.window_data[selected]
            self.show_window_info()
    
    def show_window_info(self):
        """显示窗口信息"""
        if not self.selected_hwnd:
            return
        
        try:
            # 获取窗口信息
            title = win32gui.GetWindowText(self.selected_hwnd)
            rect = win32gui.GetWindowRect(self.selected_hwnd)
            is_visible = win32gui.IsWindowVisible(self.selected_hwnd)
            is_window = win32gui.IsWindow(self.selected_hwnd)
            
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            info = f"""窗口句柄: {self.selected_hwnd}
窗口标题: {title}
窗口位置: ({rect[0]}, {rect[1]})
窗口大小: {width} x {height}
是否可见: {is_visible}
是否有效: {is_window}
窗口矩形: {rect}"""
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info)
            
        except Exception as e:
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, f"获取窗口信息失败: {str(e)}")
    
    def test_win32_capture(self):
        """测试Win32捕获"""
        if not self.selected_hwnd:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        self.status_var.set("正在测试Win32捕获...")
        
        try:
            image = self.screen_capture.capture_window_win32(self.selected_hwnd)
            if image is not None:
                self.current_image = image
                self.display_image(image)
                self.status_var.set("Win32捕获成功")
            else:
                self.status_var.set("Win32捕获失败")
                messagebox.showerror("失败", "Win32捕获失败")
        except Exception as e:
            self.status_var.set(f"Win32捕获错误: {str(e)}")
            messagebox.showerror("错误", f"Win32捕获错误: {str(e)}")
    
    def test_pyautogui_capture(self):
        """测试PyAutoGUI捕获"""
        if not self.selected_hwnd:
            messagebox.showwarning("警告", "请先选择窗口")
            return
        
        self.status_var.set("正在测试PyAutoGUI捕获...")
        
        try:
            image = self.screen_capture.capture_window_pyautogui(self.selected_hwnd)
            if image is not None:
                self.current_image = image
                self.display_image(image)
                self.status_var.set("PyAutoGUI捕获成功")
            else:
                self.status_var.set("PyAutoGUI捕获失败")
                messagebox.showerror("失败", "PyAutoGUI捕获失败")
        except Exception as e:
            self.status_var.set(f"PyAutoGUI捕获错误: {str(e)}")
            messagebox.showerror("错误", f"PyAutoGUI捕获错误: {str(e)}")
    
    def test_fullscreen_capture(self):
        """测试全屏捕获"""
        self.status_var.set("正在测试全屏捕获...")
        
        try:
            image = self.screen_capture.capture_full_screen()
            if image is not None:
                self.current_image = image
                self.display_image(image)
                self.status_var.set("全屏捕获成功")
            else:
                self.status_var.set("全屏捕获失败")
                messagebox.showerror("失败", "全屏捕获失败")
        except Exception as e:
            self.status_var.set(f"全屏捕获错误: {str(e)}")
            messagebox.showerror("错误", f"全屏捕获错误: {str(e)}")
    
    def display_image(self, image):
        """显示图像"""
        try:
            # 获取画布大小
            self.preview_canvas.update()
            canvas_width = self.preview_canvas.winfo_width()
            canvas_height = self.preview_canvas.winfo_height()
            
            # 计算缩放比例
            img_height, img_width = image.shape[:2]
            scale_x = canvas_width / img_width
            scale_y = canvas_height / img_height
            scale = min(scale_x, scale_y, 1.0)
            
            # 缩放图像
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            
            resized_image = cv2.resize(image, (new_width, new_height))
            rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(rgb_image)
            photo = ImageTk.PhotoImage(pil_image)
            
            # 显示图像
            self.preview_canvas.delete("all")
            x = (canvas_width - new_width) // 2
            y = (canvas_height - new_height) // 2
            self.preview_canvas.create_image(x, y, anchor=tk.NW, image=photo)
            
            # 保持引用
            self.preview_canvas.image = photo
            
        except Exception as e:
            print(f"显示图像失败: {e}")
    
    def save_screenshot(self):
        """保存截图"""
        if self.current_image is None:
            messagebox.showwarning("警告", "没有可保存的图像")
            return
        
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"capture_test_{timestamp}.png"
            
            os.makedirs("screenshots", exist_ok=True)
            filepath = os.path.join("screenshots", filename)
            
            cv2.imwrite(filepath, self.current_image)
            self.status_var.set(f"截图已保存: {filename}")
            messagebox.showinfo("成功", f"截图已保存: {filename}")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存截图失败: {str(e)}")
    
    def run(self):
        """运行测试工具"""
        self.root.mainloop()

def main():
    """主函数"""
    tool = CaptureTestTool()
    tool.run()

if __name__ == "__main__":
    main()
