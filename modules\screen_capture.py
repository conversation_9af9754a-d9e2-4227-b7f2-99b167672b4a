"""
屏幕捕获模块
用于捕获游戏窗口画面和屏幕截图
"""

import cv2
import numpy as np
import pyautogui
import win32gui
import win32ui
import win32con
from PIL import Image
import time
import threading
from typing import Optional, Tuple, List

class ScreenCapture:
    def __init__(self):
        self.game_window_title = "燕云十六声"  # 游戏窗口标题
        self.game_hwnd = None
        self.capture_region = None
        self.is_capturing = False
        
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False
        
    def find_game_window(self) -> bool:
        """查找游戏窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if self.game_window_title in window_title:
                    windows.append((hwnd, window_title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            self.game_hwnd = windows[0][0]
            print(f"找到游戏窗口: {windows[0][1]}")
            return True
        else:
            print("未找到游戏窗口")
            return False

    def get_all_windows(self) -> list:
        """获取所有可见窗口列表"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title.strip():  # 只包含有标题的窗口
                    windows.append((hwnd, window_title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows

    def set_game_window_by_title(self, window_title: str) -> bool:
        """通过窗口标题设置游戏窗口"""
        all_windows = self.get_all_windows()
        for hwnd, title in all_windows:
            if window_title in title:
                self.game_hwnd = hwnd
                print(f"手动设置游戏窗口: {title}")
                return True
        return False

    def set_game_window_by_hwnd(self, hwnd: int) -> bool:
        """通过窗口句柄设置游戏窗口"""
        try:
            if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
                self.game_hwnd = hwnd
                title = win32gui.GetWindowText(hwnd)
                print(f"手动设置游戏窗口: {title}")
                return True
        except:
            pass
        return False
    
    def get_window_rect(self) -> Optional[Tuple[int, int, int, int]]:
        """获取窗口矩形区域"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return None
        
        try:
            rect = win32gui.GetWindowRect(self.game_hwnd)
            return rect
        except:
            return None
    
    def capture_window(self, hwnd) -> Optional[np.ndarray]:
        """捕获指定窗口的画面"""
        # 方法1：使用Win32 API捕获
        result = self.capture_window_win32(hwnd)
        if result is not None:
            return result

        print("Win32捕获失败，尝试使用pyautogui方法...")

        # 方法2：使用pyautogui捕获窗口区域
        result = self.capture_window_pyautogui(hwnd)
        if result is not None:
            return result

        print("所有捕获方法都失败了")
        return None

    def capture_window_win32(self, hwnd) -> Optional[np.ndarray]:
        """使用Win32 API捕获窗口"""
        try:
            # 检查窗口是否有效
            if not win32gui.IsWindow(hwnd):
                print("窗口句柄无效")
                return None

            if not win32gui.IsWindowVisible(hwnd):
                print("窗口不可见")
                return None

            # 获取窗口矩形
            try:
                left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                width = right - left
                height = bottom - top

                if width <= 0 or height <= 0:
                    print(f"窗口大小无效: {width}x{height}")
                    return None

            except Exception as e:
                print(f"获取窗口矩形失败: {e}")
                return None

            # 尝试激活窗口
            try:
                win32gui.SetForegroundWindow(hwnd)
            except:
                pass  # 激活失败不影响捕获

            # 获取窗口DC
            hwndDC = win32gui.GetWindowDC(hwnd)
            if not hwndDC:
                print("获取窗口DC失败")
                return None

            try:
                mfcDC = win32ui.CreateDCFromHandle(hwndDC)
                saveDC = mfcDC.CreateCompatibleDC()

                # 创建bitmap
                saveBitMap = win32ui.CreateBitmap()
                saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
                saveDC.SelectObject(saveBitMap)

                # 复制窗口内容
                result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)

                if result:
                    # 获取bitmap数据
                    bmpinfo = saveBitMap.GetInfo()
                    bmpstr = saveBitMap.GetBitmapBits(True)

                    # 转换为numpy数组
                    img = np.frombuffer(bmpstr, dtype='uint8')
                    img.shape = (height, width, 4)
                    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

                    # 清理资源
                    win32gui.DeleteObject(saveBitMap.GetHandle())
                    saveDC.DeleteDC()
                    mfcDC.DeleteDC()
                    win32gui.ReleaseDC(hwnd, hwndDC)

                    print(f"Win32捕获成功: {width}x{height}")
                    return img
                else:
                    print("BitBlt操作失败")
                    # 清理资源
                    win32gui.DeleteObject(saveBitMap.GetHandle())
                    saveDC.DeleteDC()
                    mfcDC.DeleteDC()
                    win32gui.ReleaseDC(hwnd, hwndDC)
                    return None

            except Exception as e:
                print(f"Win32捕获过程错误: {e}")
                try:
                    win32gui.ReleaseDC(hwnd, hwndDC)
                except:
                    pass
                return None

        except Exception as e:
            print(f"Win32捕获错误: {e}")
            return None

    def capture_window_pyautogui(self, hwnd) -> Optional[np.ndarray]:
        """使用pyautogui捕获窗口区域"""
        try:
            # 获取窗口位置和大小
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top

            if width <= 0 or height <= 0:
                print(f"窗口大小无效: {width}x{height}")
                return None

            # 使用pyautogui截取指定区域
            screenshot = pyautogui.screenshot(region=(left, top, width, height))
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            print(f"pyautogui捕获成功: {width}x{height}")
            return img

        except Exception as e:
            print(f"pyautogui捕获错误: {e}")
            return None
    
    def capture_game_window(self) -> Optional[np.ndarray]:
        """捕获游戏窗口画面"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return None
        
        return self.capture_window(self.game_hwnd)
    
    def capture_screen_region(self, region: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """捕获屏幕指定区域"""
        try:
            x, y, width, height = region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return img
        except Exception as e:
            print(f"屏幕捕获错误: {e}")
            return None
    
    def capture_full_screen(self) -> Optional[np.ndarray]:
        """捕获全屏"""
        try:
            screenshot = pyautogui.screenshot()
            img = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return img
        except Exception as e:
            print(f"全屏捕获错误: {e}")
            return None
    
    def set_capture_region(self, region: Tuple[int, int, int, int]):
        """设置捕获区域"""
        self.capture_region = region
    
    def start_continuous_capture(self, callback, fps: int = 10):
        """开始连续捕获"""
        self.is_capturing = True
        
        def capture_loop():
            interval = 1.0 / fps
            while self.is_capturing:
                start_time = time.time()
                
                if self.capture_region:
                    img = self.capture_screen_region(self.capture_region)
                else:
                    img = self.capture_game_window()
                
                if img is not None:
                    callback(img)
                
                # 控制帧率
                elapsed = time.time() - start_time
                sleep_time = max(0, interval - elapsed)
                time.sleep(sleep_time)
        
        threading.Thread(target=capture_loop, daemon=True).start()
    
    def stop_continuous_capture(self):
        """停止连续捕获"""
        self.is_capturing = False
    
    def save_screenshot(self, filename: str, img: Optional[np.ndarray] = None):
        """保存截图"""
        if img is None:
            img = self.capture_game_window()
        
        if img is not None:
            cv2.imwrite(filename, img)
            print(f"截图已保存: {filename}")
            return True
        else:
            print("截图失败")
            return False
    
    def get_game_window_info(self) -> dict:
        """获取游戏窗口信息"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return {}
        
        try:
            rect = win32gui.GetWindowRect(self.game_hwnd)
            title = win32gui.GetWindowText(self.game_hwnd)
            
            return {
                "hwnd": self.game_hwnd,
                "title": title,
                "rect": rect,
                "width": rect[2] - rect[0],
                "height": rect[3] - rect[1]
            }
        except:
            return {}
    
    def is_game_window_active(self) -> bool:
        """检查游戏窗口是否处于活动状态"""
        if not self.game_hwnd:
            return False
        
        try:
            active_hwnd = win32gui.GetForegroundWindow()
            return active_hwnd == self.game_hwnd
        except:
            return False
    
    def activate_game_window(self) -> bool:
        """激活游戏窗口"""
        if not self.game_hwnd:
            if not self.find_game_window():
                return False
        
        try:
            win32gui.SetForegroundWindow(self.game_hwnd)
            return True
        except:
            return False

# 测试代码
if __name__ == "__main__":
    capture = ScreenCapture()
    
    # 查找游戏窗口
    if capture.find_game_window():
        print("游戏窗口信息:", capture.get_game_window_info())
        
        # 捕获一张截图
        img = capture.capture_game_window()
        if img is not None:
            cv2.imshow("Game Window", img)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
            # 保存截图
            capture.save_screenshot("game_screenshot.png", img)
    else:
        print("未找到游戏窗口，捕获全屏")
        img = capture.capture_full_screen()
        if img is not None:
            cv2.imshow("Full Screen", img)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
