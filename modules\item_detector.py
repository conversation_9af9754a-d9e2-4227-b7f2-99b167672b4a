"""
物品检测模块
使用深度学习模型检测游戏中的可采集物品
"""

import cv2
import numpy as np
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import os
import json
from typing import List, Dict, Tuple, Optional
from ultralytics import YOLO
import pickle

class ItemDetector:
    def __init__(self, model_path: str = "models/item_detector.pt"):
        self.model_path = model_path
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.class_names = []
        self.confidence_threshold = 0.5
        self.nms_threshold = 0.4
        
        # 预定义的物品类别
        self.default_classes = [
            "草药", "矿石", "木材", "宝箱", "采集点", 
            "药材", "铁矿", "铜矿", "银矿", "金矿",
            "灵芝", "人参", "何首乌", "雪莲", "天山雪莲"
        ]
        
        # 颜色检测参数（用于简单的颜色识别）
        self.color_ranges = {
            "草药": {"lower": np.array([35, 50, 50]), "upper": np.array([85, 255, 255])},  # 绿色
            "矿石": {"lower": np.array([100, 50, 50]), "upper": np.array([130, 255, 255])},  # 蓝色
            "宝箱": {"lower": np.array([10, 100, 100]), "upper": np.array([30, 255, 255])},  # 黄色
        }
        
        self.load_model()
    
    def load_model(self):
        """加载检测模型"""
        try:
            if os.path.exists(self.model_path):
                # 尝试加载YOLO模型
                self.model = YOLO(self.model_path)
                print(f"已加载模型: {self.model_path}")
                
                # 加载类别名称
                if hasattr(self.model, 'names'):
                    self.class_names = list(self.model.names.values())
                else:
                    self.class_names = self.default_classes
            else:
                print("模型文件不存在，使用预训练YOLO模型")
                self.model = YOLO('yolov8n.pt')  # 使用预训练的YOLOv8模型
                self.class_names = self.default_classes
                
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("将使用传统图像处理方法")
            self.model = None
    
    def detect_items(self, image: np.ndarray) -> List[Dict]:
        """检测图像中的物品"""
        if self.model is not None:
            return self.detect_with_yolo(image)
        else:
            return self.detect_with_traditional_cv(image)
    
    def detect_with_yolo(self, image: np.ndarray) -> List[Dict]:
        """使用YOLO模型检测物品"""
        try:
            results = self.model(image, conf=self.confidence_threshold)
            items = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # 获取类别名称
                        if class_id < len(self.class_names):
                            class_name = self.class_names[class_id]
                        else:
                            class_name = f"unknown_{class_id}"
                        
                        # 计算中心点和估算3D位置
                        center_x = int((x1 + x2) / 2)
                        center_y = int((y1 + y2) / 2)
                        
                        # 简单的深度估算（基于物品在屏幕中的位置）
                        estimated_depth = self.estimate_depth(center_x, center_y, image.shape)
                        
                        item = {
                            "type": class_name,
                            "confidence": float(confidence),
                            "bbox": [int(x1), int(y1), int(x2), int(y2)],
                            "center": [center_x, center_y],
                            "position": {
                                "x": center_x,
                                "y": center_y,
                                "z": estimated_depth
                            }
                        }
                        items.append(item)
            
            return items
            
        except Exception as e:
            print(f"YOLO检测错误: {e}")
            return []
    
    def detect_with_traditional_cv(self, image: np.ndarray) -> List[Dict]:
        """使用传统计算机视觉方法检测物品"""
        items = []
        
        # 转换为HSV颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        for item_type, color_range in self.color_ranges.items():
            # 创建颜色掩码
            mask = cv2.inRange(hsv, color_range["lower"], color_range["upper"])
            
            # 形态学操作去噪
            kernel = np.ones((5, 5), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # 过滤小的噪声区域
                    # 获取边界框
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 计算中心点
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    # 估算深度
                    estimated_depth = self.estimate_depth(center_x, center_y, image.shape)
                    
                    # 计算置信度（基于轮廓面积和形状）
                    confidence = min(area / 1000.0, 1.0)
                    
                    item = {
                        "type": item_type,
                        "confidence": confidence,
                        "bbox": [x, y, x + w, y + h],
                        "center": [center_x, center_y],
                        "position": {
                            "x": center_x,
                            "y": center_y,
                            "z": estimated_depth
                        }
                    }
                    items.append(item)
        
        return items
    
    def estimate_depth(self, x: int, y: int, image_shape: Tuple) -> float:
        """估算物品的深度（Z坐标）"""
        height, width = image_shape[:2]
        
        # 简单的深度估算：屏幕下方的物品更近
        depth_factor = y / height
        estimated_depth = 10.0 + depth_factor * 50.0  # 深度范围 10-60
        
        return estimated_depth
    
    def filter_items_by_distance(self, items: List[Dict], max_distance: float = 100.0) -> List[Dict]:
        """根据距离过滤物品"""
        filtered_items = []
        for item in items:
            distance = item["position"]["z"]
            if distance <= max_distance:
                filtered_items.append(item)
        
        return filtered_items
    
    def sort_items_by_distance(self, items: List[Dict]) -> List[Dict]:
        """按距离排序物品（近到远）"""
        return sorted(items, key=lambda x: x["position"]["z"])
    
    def draw_detections(self, image: np.ndarray, items: List[Dict]) -> np.ndarray:
        """在图像上绘制检测结果"""
        result_image = image.copy()
        
        for item in items:
            bbox = item["bbox"]
            item_type = item["type"]
            confidence = item["confidence"]
            
            # 绘制边界框
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (0, 255, 0), 2)
            
            # 绘制标签
            label = f"{item_type}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (bbox[0], bbox[1] - label_size[1] - 10), 
                         (bbox[0] + label_size[0], bbox[1]), (0, 255, 0), -1)
            cv2.putText(result_image, label, (bbox[0], bbox[1] - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
            
            # 绘制中心点
            center = item["center"]
            cv2.circle(result_image, tuple(center), 5, (255, 0, 0), -1)
        
        return result_image
    
    def save_detection_result(self, image: np.ndarray, items: List[Dict], filename: str):
        """保存检测结果"""
        result_image = self.draw_detections(image, items)
        cv2.imwrite(filename, result_image)
        
        # 保存检测数据
        data_filename = filename.replace('.png', '.json').replace('.jpg', '.json')
        with open(data_filename, 'w', encoding='utf-8') as f:
            json.dump(items, f, ensure_ascii=False, indent=2)
    
    def update_model(self, new_model_path: str):
        """更新检测模型"""
        if os.path.exists(new_model_path):
            self.model_path = new_model_path
            self.load_model()
            print(f"模型已更新: {new_model_path}")
        else:
            print(f"模型文件不存在: {new_model_path}")
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        self.confidence_threshold = max(0.0, min(1.0, threshold))
        print(f"置信度阈值已设置为: {self.confidence_threshold}")

# 测试代码
if __name__ == "__main__":
    detector = ItemDetector()
    
    # 测试图像检测
    test_image_path = "test_image.jpg"
    if os.path.exists(test_image_path):
        image = cv2.imread(test_image_path)
        items = detector.detect_items(image)
        
        print(f"检测到 {len(items)} 个物品:")
        for item in items:
            print(f"- {item['type']}: 置信度 {item['confidence']:.2f}, 位置 {item['position']}")
        
        # 保存检测结果
        detector.save_detection_result(image, items, "detection_result.jpg")
        
        # 显示结果
        result_image = detector.draw_detections(image, items)
        cv2.imshow("Detection Result", result_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        print("测试图像不存在，请提供测试图像")
