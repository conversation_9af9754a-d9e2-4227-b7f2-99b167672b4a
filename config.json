{"game": {"window_title": "燕云十六声", "capture_fps": 10, "movement_speed": 1.0, "turn_speed": 1.0}, "detection": {"confidence_threshold": 0.5, "nms_threshold": 0.4, "max_detection_distance": 100.0, "model_path": "models/item_detector.pt"}, "pathfinding": {"grid_size": 1.0, "precision_threshold": 5.0, "max_iterations": 10000, "movement_costs": {"horizontal": 1.0, "vertical": 1.4, "climb": 2.0}}, "ui": {"window_width": 800, "window_height": 600, "theme": "default"}, "hotkeys": {"start_stop": "F1", "emergency_stop": "F2", "manual_collect": "F3"}}