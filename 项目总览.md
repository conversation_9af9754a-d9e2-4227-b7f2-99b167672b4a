# 燕云十六声自动寻路采集程序 - 项目总览

## 🎯 项目简介

这是一个为燕云十六声游戏开发的智能自动化工具，集成了AI物品识别、自动寻路、智能采集等功能。程序采用模块化设计，支持自定义AI模型训练，具有完整的图形用户界面。

## 📁 项目文件结构

```
燕云十六声自动寻路采集程序/
├── 📄 main.py                 # 主程序入口，包含完整的GUI界面
├── 🚀 run.py                  # 启动脚本，检查环境并启动程序
├── ⚙️ install.py              # 自动安装脚本，安装所有依赖
├── 🧪 test_system.py          # 系统测试脚本，验证各模块功能
├── 📋 requirements.txt        # Python依赖包列表
├── ⚙️ config.json            # 程序配置文件（运行时自动生成）
├── 📖 README.md              # 详细使用说明文档
├── 📝 项目总览.md            # 本文件，项目概述
├── 🖱️ 启动程序.bat           # Windows批处理启动文件
├── 📦 安装依赖.bat           # Windows批处理安装文件
└── 📂 modules/               # 核心功能模块目录
    ├── 📄 __init__.py
    ├── 📷 screen_capture.py   # 屏幕捕获模块
    ├── 🎮 game_controller.py  # 游戏控制模块
    ├── 🤖 item_detector.py    # AI物品检测模块
    ├── 🗺️ pathfinding.py      # A*寻路算法模块
    ├── 🧠 ai_trainer.py       # AI训练工具模块
    └── ⚙️ config_manager.py   # 配置管理模块
```

## 🔧 核心功能模块

### 1. 屏幕捕获模块 (screen_capture.py)
- **功能**: 实时捕获游戏窗口画面
- **特性**: 
  - 自动检测游戏窗口
  - 支持全屏和窗口捕获
  - 连续捕获和单次截图
  - 窗口状态检测

### 2. 游戏控制模块 (game_controller.py)
- **功能**: 模拟键盘鼠标操作控制游戏角色
- **特性**:
  - WASD移动控制
  - 鼠标转向控制
  - 自动寻路移动
  - 物品采集交互
  - 紧急停止功能

### 3. AI物品检测模块 (item_detector.py)
- **功能**: 使用深度学习识别游戏中的可采集物品
- **特性**:
  - 支持YOLO模型
  - 传统CV方法备选
  - 多种物品类别识别
  - 置信度和距离过滤
  - 检测结果可视化

### 4. 自动寻路模块 (pathfinding.py)
- **功能**: 3D环境下的智能路径规划
- **特性**:
  - A*寻路算法
  - 3D坐标支持
  - 障碍物避让
  - 路径平滑优化
  - 地图数据管理

### 5. AI训练工具 (ai_trainer.py)
- **功能**: 数据标注和模型训练
- **特性**:
  - 图形化标注界面
  - 多类别物品标注
  - YOLO模型训练
  - 模型测试验证
  - 训练进度监控

### 6. 配置管理模块 (config_manager.py)
- **功能**: 程序参数配置管理
- **特性**:
  - JSON配置文件
  - 默认配置生成
  - 动态配置更新
  - 分类配置管理

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保已安装Python 3.8+
python --version

# 运行安装脚本
python install.py
# 或使用批处理文件
双击 "安装依赖.bat"
```

### 2. 启动程序
```bash
# 使用启动脚本
python run.py
# 或使用批处理文件
双击 "启动程序.bat"
```

### 3. 首次使用
1. 启动游戏（确保窗口标题包含"燕云十六声"）
2. 点击"开始预览"查看检测效果
3. 使用"AI训练工具"标注训练数据
4. 调整"设置"中的参数
5. 开始"自动采集"

## 🎮 使用流程

### 自动采集模式
1. **准备阶段**
   - 启动游戏并进入游戏世界
   - 确保角色处于安全位置
   - 启动本程序

2. **检测阶段**
   - 程序自动捕获游戏画面
   - AI模型识别可采集物品
   - 实时显示检测结果

3. **寻路阶段**
   - 选择最近的目标物品
   - 计算最优路径
   - 自动控制角色移动

4. **采集阶段**
   - 到达物品位置
   - 自动执行采集操作
   - 继续寻找下一个目标

### AI训练模式
1. **数据收集**
   - 在游戏中截取包含物品的图像
   - 确保图像质量和多样性

2. **数据标注**
   - 使用标注工具框选物品
   - 选择正确的物品类别
   - 保存标注数据

3. **模型训练**
   - 设置训练参数
   - 开始模型训练
   - 监控训练进度

4. **模型部署**
   - 测试训练效果
   - 保存训练好的模型
   - 在主程序中使用新模型

## ⚙️ 配置说明

### 游戏配置
- `movement_speed`: 角色移动速度 (0.1-3.0)
- `turn_speed`: 角色转向速度 (0.1-3.0)
- `window_title`: 游戏窗口标题关键词

### 检测配置
- `confidence_threshold`: AI检测置信度阈值 (0.1-1.0)
- `max_detection_distance`: 最大检测距离
- `model_path`: AI模型文件路径

### 寻路配置
- `grid_size`: 寻路网格大小
- `precision_threshold`: 到达目标的精度要求
- `movement_costs`: 不同移动方式的代价

## 🔍 故障排除

### 常见问题
1. **无法检测游戏窗口**
   - 检查游戏窗口标题
   - 确保游戏在前台运行
   - 修改配置文件中的window_title

2. **物品识别不准确**
   - 收集更多训练数据
   - 调整置信度阈值
   - 重新训练AI模型

3. **寻路卡住或失败**
   - 检查起点和终点是否可达
   - 调整网格大小参数
   - 更新地图障碍物数据

4. **程序运行缓慢**
   - 降低捕获帧率
   - 关闭实时预览
   - 使用GPU加速

### 调试方法
1. 查看程序日志输出
2. 运行系统测试脚本
3. 检查配置文件设置
4. 验证依赖包安装

## 📈 性能优化

### 系统要求
- **最低配置**: 4GB内存，集成显卡
- **推荐配置**: 8GB内存，独立显卡
- **最佳配置**: 16GB内存，支持CUDA的显卡

### 优化建议
1. **硬件优化**
   - 使用SSD提高文件读写速度
   - 增加内存减少交换文件使用
   - 使用独立显卡加速AI计算

2. **软件优化**
   - 关闭不必要的后台程序
   - 调整游戏画质设置
   - 优化程序参数配置

## 🛡️ 安全提醒

⚠️ **重要声明**
- 本程序仅供学习和研究使用
- 请遵守游戏服务条款
- 使用自动化工具存在封号风险
- 建议在测试环境中使用
- 不承担任何使用风险

## 📞 技术支持

如需帮助，请：
1. 仔细阅读README.md文档
2. 运行test_system.py检查系统状态
3. 查看程序运行日志
4. 检查配置文件设置

## 🔄 版本信息

**当前版本**: v1.0.0
**发布日期**: 2024年
**开发语言**: Python 3.8+
**主要依赖**: OpenCV, PyTorch, tkinter, ultralytics

---

*感谢使用燕云十六声自动寻路采集程序！*
