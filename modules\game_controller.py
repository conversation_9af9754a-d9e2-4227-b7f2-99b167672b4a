"""
游戏控制模块
用于模拟键盘和鼠标操作控制游戏角色
"""

import time
import math
import threading
from typing import List, Dict, Tuple, Optional
import pyautogui
import pynput
from pynput import keyboard, mouse
import win32api
import win32con

class GameController:
    def __init__(self):
        self.is_moving = False
        self.current_keys = set()
        self.move_thread = None
        self.stop_movement = False
        
        # 键位映射
        self.key_mapping = {
            'forward': 'w',
            'backward': 's',
            'left': 'a',
            'right': 'd',
            'jump': 'space',
            'interact': 'f',
            'run': 'shift',
            'crouch': 'ctrl'
        }
        
        # 移动参数
        self.move_speed = 1.0
        self.turn_speed = 1.0
        self.precision_threshold = 5.0  # 到达目标的精度阈值
        
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False
        
    def press_key(self, key: str, duration: float = 0.1):
        """按下并释放按键"""
        try:
            pyautogui.keyDown(key)
            time.sleep(duration)
            pyautogui.keyUp(key)
        except Exception as e:
            print(f"按键操作错误: {e}")
    
    def hold_key(self, key: str):
        """持续按住按键"""
        try:
            if key not in self.current_keys:
                pyautogui.keyDown(key)
                self.current_keys.add(key)
        except Exception as e:
            print(f"按键按住错误: {e}")
    
    def release_key(self, key: str):
        """释放按键"""
        try:
            if key in self.current_keys:
                pyautogui.keyUp(key)
                self.current_keys.remove(key)
        except Exception as e:
            print(f"按键释放错误: {e}")
    
    def release_all_keys(self):
        """释放所有按键"""
        for key in list(self.current_keys):
            self.release_key(key)
    
    def move_forward(self, duration: float = 1.0):
        """向前移动"""
        self.hold_key(self.key_mapping['forward'])
        time.sleep(duration)
        self.release_key(self.key_mapping['forward'])
    
    def move_backward(self, duration: float = 1.0):
        """向后移动"""
        self.hold_key(self.key_mapping['backward'])
        time.sleep(duration)
        self.release_key(self.key_mapping['backward'])
    
    def move_left(self, duration: float = 1.0):
        """向左移动"""
        self.hold_key(self.key_mapping['left'])
        time.sleep(duration)
        self.release_key(self.key_mapping['left'])
    
    def move_right(self, duration: float = 1.0):
        """向右移动"""
        self.hold_key(self.key_mapping['right'])
        time.sleep(duration)
        self.release_key(self.key_mapping['right'])
    
    def turn_left(self, angle: float = 90):
        """向左转向"""
        # 计算鼠标移动距离（这里需要根据游戏的鼠标灵敏度调整）
        mouse_move = int(angle * self.turn_speed)
        pyautogui.moveRel(-mouse_move, 0)
    
    def turn_right(self, angle: float = 90):
        """向右转向"""
        mouse_move = int(angle * self.turn_speed)
        pyautogui.moveRel(mouse_move, 0)
    
    def turn_to_angle(self, target_angle: float, current_angle: float):
        """转向到指定角度"""
        angle_diff = target_angle - current_angle
        
        # 标准化角度差到 -180 到 180 度之间
        while angle_diff > 180:
            angle_diff -= 360
        while angle_diff < -180:
            angle_diff += 360
        
        if abs(angle_diff) > 5:  # 如果角度差大于5度才转向
            if angle_diff > 0:
                self.turn_right(abs(angle_diff))
            else:
                self.turn_left(abs(angle_diff))
    
    def jump(self):
        """跳跃"""
        self.press_key(self.key_mapping['jump'], 0.1)
    
    def interact(self):
        """交互（采集物品）"""
        self.press_key(self.key_mapping['interact'], 0.1)
    
    def run_toggle(self):
        """切换跑步状态"""
        self.press_key(self.key_mapping['run'], 0.1)
    
    def calculate_direction(self, current_pos: Dict, target_pos: Dict) -> float:
        """计算从当前位置到目标位置的方向角度"""
        dx = target_pos['x'] - current_pos['x']
        dy = target_pos['y'] - current_pos['y']
        
        # 计算角度（弧度转度数）
        angle = math.degrees(math.atan2(dy, dx))
        return angle
    
    def calculate_distance(self, pos1: Dict, pos2: Dict) -> float:
        """计算两点之间的距离"""
        dx = pos2['x'] - pos1['x']
        dy = pos2['y'] - pos1['y']
        dz = pos2.get('z', 0) - pos1.get('z', 0)
        
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def move_to_position(self, current_pos: Dict, target_pos: Dict, current_angle: float = 0):
        """移动到指定位置"""
        distance = self.calculate_distance(current_pos, target_pos)
        
        if distance < self.precision_threshold:
            print("已到达目标位置")
            return True
        
        # 计算需要转向的角度
        target_angle = self.calculate_direction(current_pos, target_pos)
        self.turn_to_angle(target_angle, current_angle)
        
        # 计算移动时间（根据距离和移动速度）
        move_time = min(distance / 100.0 * self.move_speed, 2.0)  # 最大移动时间2秒
        
        # 开始移动
        self.move_forward(move_time)
        
        return False
    
    def follow_path(self, path: List[Dict]):
        """沿着路径移动"""
        if not path:
            return
        
        self.is_moving = True
        self.stop_movement = False
        
        def move_along_path():
            current_angle = 0
            
            for i, waypoint in enumerate(path):
                if self.stop_movement:
                    break
                
                print(f"移动到路径点 {i+1}/{len(path)}: {waypoint}")
                
                # 这里需要获取当前位置，暂时使用上一个路径点作为当前位置
                if i == 0:
                    current_pos = {"x": 0, "y": 0, "z": 0}  # 起始位置
                else:
                    current_pos = path[i-1]
                
                # 移动到当前路径点
                while not self.stop_movement:
                    arrived = self.move_to_position(current_pos, waypoint, current_angle)
                    if arrived:
                        break
                    
                    # 更新当前位置（这里需要从游戏中获取实际位置）
                    # current_pos = self.get_current_position()
                    
                    time.sleep(0.1)
                
                # 到达路径点后稍作停顿
                time.sleep(0.2)
            
            self.is_moving = False
            print("路径移动完成")
        
        # 在新线程中执行移动
        self.move_thread = threading.Thread(target=move_along_path, daemon=True)
        self.move_thread.start()
    
    def stop_movement_immediately(self):
        """立即停止移动"""
        self.stop_movement = True
        self.release_all_keys()
        self.is_moving = False
    
    def smooth_move_to_target(self, current_pos: Dict, target_pos: Dict, 
                            current_angle: float = 0, precision: float = 5.0):
        """平滑移动到目标位置"""
        self.is_moving = True
        self.stop_movement = False
        
        def smooth_movement():
            while not self.stop_movement:
                distance = self.calculate_distance(current_pos, target_pos)
                
                if distance < precision:
                    print("已到达目标位置")
                    break
                
                # 计算移动方向
                target_angle = self.calculate_direction(current_pos, target_pos)
                
                # 平滑转向
                angle_diff = target_angle - current_angle
                while angle_diff > 180:
                    angle_diff -= 360
                while angle_diff < -180:
                    angle_diff += 360
                
                # 如果角度差较大，先转向
                if abs(angle_diff) > 10:
                    if angle_diff > 0:
                        self.turn_right(min(abs(angle_diff), 30))
                    else:
                        self.turn_left(min(abs(angle_diff), 30))
                    current_angle += angle_diff * 0.3  # 部分转向
                    time.sleep(0.1)
                    continue
                
                # 移动一小段距离
                move_duration = min(0.5, distance / 100.0)
                self.move_forward(move_duration)
                
                # 更新位置（这里需要从游戏中获取实际位置）
                # current_pos = self.get_current_position()
                
                time.sleep(0.1)
            
            self.is_moving = False
        
        threading.Thread(target=smooth_movement, daemon=True).start()
    
    def auto_collect_item(self, item_position: Dict, current_position: Dict):
        """自动采集物品"""
        print(f"开始采集物品，位置: {item_position}")
        
        # 移动到物品位置
        distance = self.calculate_distance(current_position, item_position)
        
        if distance > 3.0:  # 如果距离较远，先移动过去
            self.move_to_position(current_position, item_position)
            time.sleep(1.0)  # 等待移动完成
        
        # 尝试交互采集
        for _ in range(3):  # 尝试3次
            self.interact()
            time.sleep(0.5)
        
        print("物品采集完成")
    
    def emergency_stop(self):
        """紧急停止所有操作"""
        self.stop_movement_immediately()
        print("紧急停止所有操作")

# 测试代码
if __name__ == "__main__":
    controller = GameController()
    
    print("游戏控制器测试")
    print("按 Enter 开始测试移动...")
    input()
    
    # 测试基本移动
    print("向前移动2秒")
    controller.move_forward(2.0)
    
    print("向右转90度")
    controller.turn_right(90)
    
    print("向前移动1秒")
    controller.move_forward(1.0)
    
    print("跳跃")
    controller.jump()
    
    print("交互")
    controller.interact()
    
    print("测试完成")
