"""
AI训练模块
用于训练物品识别模型和数据标注
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image, ImageTk
import threading
from typing import List, Dict, Tuple
from ultralytics import YOLO

class AnnotationTool:
    def __init__(self, parent_window=None):
        self.parent = parent_window
        self.window = None
        self.canvas = None
        self.image = None
        self.photo = None
        self.annotations = []
        self.current_annotation = None
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        
        # 类别列表
        self.classes = [
            "草药", "矿石", "木材", "宝箱", "采集点", 
            "药材", "铁矿", "铜矿", "银矿", "金矿",
            "灵芝", "人参", "何首乌", "雪莲", "天山雪莲"
        ]
        self.current_class = "草药"
        
        # 数据存储
        self.dataset_path = "training_data"
        self.images_path = os.path.join(self.dataset_path, "images")
        self.labels_path = os.path.join(self.dataset_path, "labels")
        
        # 创建目录
        os.makedirs(self.images_path, exist_ok=True)
        os.makedirs(self.labels_path, exist_ok=True)
    
    def open_annotation_window(self):
        """打开标注窗口"""
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("AI训练 - 数据标注工具")
        self.window.geometry("1200x800")
        
        self.setup_annotation_ui()
    
    def setup_annotation_ui(self):
        """设置标注界面"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=200)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图像显示区域
        image_frame = ttk.Frame(main_frame)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 控制面板内容
        ttk.Label(control_frame, text="数据标注工具", font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # 文件操作
        file_frame = ttk.LabelFrame(control_frame, text="文件操作", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="加载图像", command=self.load_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="保存标注", command=self.save_annotations).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(file_frame, text="加载标注", command=self.load_annotations).pack(fill=tk.X)
        
        # 类别选择
        class_frame = ttk.LabelFrame(control_frame, text="物品类别", padding="10")
        class_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.class_var = tk.StringVar(value=self.current_class)
        class_combo = ttk.Combobox(class_frame, textvariable=self.class_var, values=self.classes, state="readonly")
        class_combo.pack(fill=tk.X)
        class_combo.bind("<<ComboboxSelected>>", self.on_class_changed)
        
        # 标注操作
        annotation_frame = ttk.LabelFrame(control_frame, text="标注操作", padding="10")
        annotation_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(annotation_frame, text="清除所有标注", command=self.clear_annotations).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(annotation_frame, text="删除最后标注", command=self.delete_last_annotation).pack(fill=tk.X)
        
        # 标注列表
        list_frame = ttk.LabelFrame(control_frame, text="标注列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.annotation_listbox = tk.Listbox(list_frame)
        self.annotation_listbox.pack(fill=tk.BOTH, expand=True)
        self.annotation_listbox.bind("<Double-Button-1>", self.on_annotation_selected)
        
        # 图像画布
        canvas_frame = ttk.Frame(image_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_release)
        
        # 状态栏
        self.status_var = tk.StringVar(value="请加载图像开始标注")
        status_bar = ttk.Label(self.window, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def load_image(self):
        """加载图像"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.image = cv2.imread(file_path)
            if self.image is not None:
                self.display_image()
                self.annotations = []
                self.update_annotation_list()
                self.status_var.set(f"已加载图像: {os.path.basename(file_path)}")
            else:
                messagebox.showerror("错误", "无法加载图像文件")
    
    def display_image(self):
        """显示图像"""
        if self.image is None:
            return
        
        # 获取画布大小
        self.canvas.update()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        # 计算缩放比例
        img_height, img_width = self.image.shape[:2]
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        self.scale = min(scale_x, scale_y, 1.0)  # 不放大图像
        
        # 缩放图像
        new_width = int(img_width * self.scale)
        new_height = int(img_height * self.scale)
        
        resized_image = cv2.resize(self.image, (new_width, new_height))
        rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(rgb_image)
        self.photo = ImageTk.PhotoImage(pil_image)
        
        # 清除画布并显示图像
        self.canvas.delete("all")
        self.canvas.create_image(canvas_width//2, canvas_height//2, image=self.photo)
        
        # 重新绘制标注
        self.draw_annotations()
    
    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if self.image is None:
            return
        
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y
        self.current_annotation = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline="red", width=2
        )
    
    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if self.drawing and self.current_annotation:
            self.canvas.coords(self.current_annotation, self.start_x, self.start_y, event.x, event.y)
    
    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if self.drawing and self.current_annotation:
            self.drawing = False
            
            # 计算边界框（转换为原图坐标）
            x1 = min(self.start_x, event.x) / self.scale
            y1 = min(self.start_y, event.y) / self.scale
            x2 = max(self.start_x, event.x) / self.scale
            y2 = max(self.start_y, event.y) / self.scale
            
            # 检查边界框大小
            if abs(x2 - x1) > 10 and abs(y2 - y1) > 10:
                annotation = {
                    "class": self.current_class,
                    "bbox": [x1, y1, x2, y2],
                    "canvas_id": self.current_annotation
                }
                self.annotations.append(annotation)
                self.update_annotation_list()
                self.status_var.set(f"添加标注: {self.current_class}")
            else:
                self.canvas.delete(self.current_annotation)
            
            self.current_annotation = None
    
    def on_class_changed(self, event):
        """类别改变事件"""
        self.current_class = self.class_var.get()
    
    def draw_annotations(self):
        """绘制所有标注"""
        for annotation in self.annotations:
            bbox = annotation["bbox"]
            x1, y1, x2, y2 = [coord * self.scale for coord in bbox]
            
            canvas_id = self.canvas.create_rectangle(
                x1, y1, x2, y2, outline="red", width=2
            )
            annotation["canvas_id"] = canvas_id
            
            # 添加类别标签
            self.canvas.create_text(
                x1, y1 - 10, text=annotation["class"],
                fill="red", anchor="sw", font=("Arial", 10)
            )
    
    def update_annotation_list(self):
        """更新标注列表"""
        self.annotation_listbox.delete(0, tk.END)
        for i, annotation in enumerate(self.annotations):
            self.annotation_listbox.insert(tk.END, f"{i+1}. {annotation['class']}")
    
    def clear_annotations(self):
        """清除所有标注"""
        for annotation in self.annotations:
            if "canvas_id" in annotation:
                self.canvas.delete(annotation["canvas_id"])
        self.annotations = []
        self.update_annotation_list()
        self.status_var.set("已清除所有标注")
    
    def delete_last_annotation(self):
        """删除最后一个标注"""
        if self.annotations:
            last_annotation = self.annotations.pop()
            if "canvas_id" in last_annotation:
                self.canvas.delete(last_annotation["canvas_id"])
            self.update_annotation_list()
            self.status_var.set("已删除最后一个标注")
    
    def save_annotations(self):
        """保存标注"""
        if not self.annotations:
            messagebox.showwarning("警告", "没有标注数据可保存")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存标注文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            # 准备保存的数据
            save_data = {
                "image_info": {
                    "width": self.image.shape[1],
                    "height": self.image.shape[0]
                },
                "annotations": [
                    {
                        "class": ann["class"],
                        "bbox": ann["bbox"]
                    }
                    for ann in self.annotations
                ]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            self.status_var.set(f"标注已保存: {os.path.basename(file_path)}")
    
    def load_annotations(self):
        """加载标注"""
        file_path = filedialog.askopenfilename(
            title="加载标注文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.clear_annotations()
                
                for ann_data in data.get("annotations", []):
                    annotation = {
                        "class": ann_data["class"],
                        "bbox": ann_data["bbox"]
                    }
                    self.annotations.append(annotation)
                
                self.draw_annotations()
                self.update_annotation_list()
                self.status_var.set(f"标注已加载: {os.path.basename(file_path)}")
                
            except Exception as e:
                messagebox.showerror("错误", f"加载标注失败: {str(e)}")

class AITrainer:
    def __init__(self):
        self.annotation_tool = None
        self.training_window = None
        self.model = None
        self.training_data_path = "training_data"
        self.model_save_path = "models"
        
        # 创建目录
        os.makedirs(self.training_data_path, exist_ok=True)
        os.makedirs(self.model_save_path, exist_ok=True)
    
    def open_trainer_window(self):
        """打开训练器窗口"""
        self.training_window = tk.Toplevel()
        self.training_window.title("AI训练工具")
        self.training_window.geometry("600x500")
        
        self.setup_trainer_ui()
    
    def setup_trainer_ui(self):
        """设置训练器界面"""
        main_frame = ttk.Frame(self.training_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="AI训练工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 数据标注区域
        annotation_frame = ttk.LabelFrame(main_frame, text="数据标注", padding="15")
        annotation_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(annotation_frame, text="使用标注工具为训练数据添加标签").pack(anchor=tk.W)
        ttk.Button(annotation_frame, text="打开标注工具", 
                  command=self.open_annotation_tool).pack(pady=(10, 0))
        
        # 模型训练区域
        training_frame = ttk.LabelFrame(main_frame, text="模型训练", padding="15")
        training_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(training_frame, text="训练参数设置:").pack(anchor=tk.W)
        
        # 训练参数
        params_frame = ttk.Frame(training_frame)
        params_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(params_frame, text="训练轮数:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.epochs_var = tk.StringVar(value="100")
        ttk.Entry(params_frame, textvariable=self.epochs_var, width=10).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(params_frame, text="批次大小:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.batch_size_var = tk.StringVar(value="16")
        ttk.Entry(params_frame, textvariable=self.batch_size_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        ttk.Button(training_frame, text="开始训练", 
                  command=self.start_training).pack(pady=(15, 0))
        
        # 模型管理区域
        model_frame = ttk.LabelFrame(main_frame, text="模型管理", padding="15")
        model_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(model_frame, text="加载预训练模型", 
                  command=self.load_pretrained_model).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(model_frame, text="保存当前模型", 
                  command=self.save_model).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(model_frame, text="测试模型", 
                  command=self.test_model).pack(side=tk.LEFT)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="训练进度", padding="15")
        progress_frame.pack(fill=tk.BOTH, expand=True)
        
        self.progress_var = tk.StringVar(value="等待开始训练...")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))
    
    def open_annotation_tool(self):
        """打开标注工具"""
        if self.annotation_tool is None:
            self.annotation_tool = AnnotationTool(self.training_window)
        self.annotation_tool.open_annotation_window()
    
    def start_training(self):
        """开始训练"""
        try:
            epochs = int(self.epochs_var.get())
            batch_size = int(self.batch_size_var.get())
            
            self.progress_var.set("正在准备训练数据...")
            self.progress_bar['value'] = 0
            
            # 在新线程中进行训练
            threading.Thread(target=self.train_model, args=(epochs, batch_size), daemon=True).start()
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的训练参数")
    
    def train_model(self, epochs: int, batch_size: int):
        """训练模型"""
        try:
            # 使用YOLO进行训练
            self.model = YOLO('yolov8n.pt')  # 加载预训练模型
            
            # 准备数据集配置
            dataset_config = {
                'train': self.training_data_path,
                'val': self.training_data_path,
                'nc': len(self.annotation_tool.classes) if self.annotation_tool else 15,
                'names': self.annotation_tool.classes if self.annotation_tool else [
                    "草药", "矿石", "木材", "宝箱", "采集点", 
                    "药材", "铁矿", "铜矿", "银矿", "金矿",
                    "灵芝", "人参", "何首乌", "雪莲", "天山雪莲"
                ]
            }
            
            # 保存数据集配置
            config_path = os.path.join(self.training_data_path, "dataset.yaml")
            with open(config_path, 'w', encoding='utf-8') as f:
                import yaml
                yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
            
            self.progress_var.set("开始训练模型...")
            
            # 训练模型
            results = self.model.train(
                data=config_path,
                epochs=epochs,
                batch=batch_size,
                project=self.model_save_path,
                name='item_detector'
            )
            
            self.progress_var.set("训练完成！")
            self.progress_bar['value'] = 100
            
            messagebox.showinfo("成功", "模型训练完成！")
            
        except Exception as e:
            self.progress_var.set(f"训练失败: {str(e)}")
            messagebox.showerror("错误", f"训练失败: {str(e)}")
    
    def load_pretrained_model(self):
        """加载预训练模型"""
        file_path = filedialog.askopenfilename(
            title="选择模型文件",
            filetypes=[("模型文件", "*.pt *.pth"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.model = YOLO(file_path)
                self.progress_var.set(f"已加载模型: {os.path.basename(file_path)}")
                messagebox.showinfo("成功", "模型加载成功！")
            except Exception as e:
                messagebox.showerror("错误", f"模型加载失败: {str(e)}")
    
    def save_model(self):
        """保存模型"""
        if self.model is None:
            messagebox.showwarning("警告", "没有可保存的模型")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存模型",
            defaultextension=".pt",
            filetypes=[("模型文件", "*.pt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 保存模型
                torch.save(self.model.model.state_dict(), file_path)
                self.progress_var.set(f"模型已保存: {os.path.basename(file_path)}")
                messagebox.showinfo("成功", "模型保存成功！")
            except Exception as e:
                messagebox.showerror("错误", f"模型保存失败: {str(e)}")
    
    def test_model(self):
        """测试模型"""
        if self.model is None:
            messagebox.showwarning("警告", "请先加载或训练模型")
            return
        
        file_path = filedialog.askopenfilename(
            title="选择测试图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 进行预测
                results = self.model(file_path)
                
                # 显示结果
                for result in results:
                    result.show()
                
                self.progress_var.set("模型测试完成")
                
            except Exception as e:
                messagebox.showerror("错误", f"模型测试失败: {str(e)}")

# 测试代码
if __name__ == "__main__":
    trainer = AITrainer()
    trainer.open_trainer_window()
    
    # 如果是独立运行，启动主循环
    if trainer.training_window:
        trainer.training_window.mainloop()
