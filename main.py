"""
燕云十六声自动寻路和物品采集程序
主程序入口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
from pathlib import Path

# 导入自定义模块
from modules.screen_capture import ScreenCapture
from modules.game_controller import GameController
from modules.item_detector import ItemDetector
from modules.pathfinding import PathFinder
from modules.ai_trainer import AITrainer
from modules.config_manager import ConfigManager

class YanYunAutoBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("燕云十六声 - 自动寻路采集助手")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.screen_capture = ScreenCapture()
        self.game_controller = GameController()
        self.item_detector = ItemDetector()
        self.path_finder = PathFinder()
        self.ai_trainer = AITrainer()
        
        # 状态变量
        self.is_running = False
        self.current_position = {"x": 0, "y": 0, "z": 0}
        self.target_position = {"x": 0, "y": 0, "z": 0}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="燕云十六声 - 自动寻路采集助手", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 游戏状态区域
        status_frame = ttk.LabelFrame(main_frame, text="游戏状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 当前位置显示
        ttk.Label(status_frame, text="当前位置:").grid(row=0, column=0, sticky=tk.W)
        self.position_var = tk.StringVar(value="X: 0, Y: 0, Z: 0")
        ttk.Label(status_frame, textvariable=self.position_var).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 运行状态
        ttk.Label(status_frame, text="运行状态:").grid(row=1, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="待机中")
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮区域
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 开始/停止按钮
        self.start_button = ttk.Button(control_frame, text="开始自动采集", 
                                      command=self.toggle_auto_collection)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        # 手动控制按钮
        ttk.Button(control_frame, text="手动寻路", 
                  command=self.manual_pathfinding).grid(row=0, column=1, padx=(0, 10))
        
        # AI训练按钮
        ttk.Button(control_frame, text="AI训练工具", 
                  command=self.open_ai_trainer).grid(row=0, column=2, padx=(0, 10))
        
        # 设置按钮
        ttk.Button(control_frame, text="设置", 
                  command=self.open_settings).grid(row=0, column=3)
        
        # 目标设置区域
        target_frame = ttk.LabelFrame(main_frame, text="目标设置", padding="10")
        target_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 目标坐标输入
        ttk.Label(target_frame, text="目标坐标:").grid(row=0, column=0, sticky=tk.W)
        
        coord_frame = ttk.Frame(target_frame)
        coord_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(coord_frame, text="X:").grid(row=0, column=0)
        self.target_x = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=self.target_x, width=8).grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(coord_frame, text="Y:").grid(row=0, column=2)
        self.target_y = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=self.target_y, width=8).grid(row=0, column=3, padx=(5, 10))
        
        ttk.Label(coord_frame, text="Z:").grid(row=0, column=4)
        self.target_z = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=self.target_z, width=8).grid(row=0, column=5, padx=(5, 10))
        
        ttk.Button(coord_frame, text="设置目标", 
                  command=self.set_target).grid(row=0, column=6, padx=(10, 0))
        
        # 检测结果显示区域
        detection_frame = ttk.LabelFrame(main_frame, text="检测结果", padding="10")
        detection_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 检测到的物品列表
        items_frame = ttk.Frame(detection_frame)
        items_frame.pack(fill=tk.X)

        ttk.Label(items_frame, text="检测到的物品:").pack(side=tk.LEFT)
        self.items_count_var = tk.StringVar(value="0")
        ttk.Label(items_frame, textvariable=self.items_count_var).pack(side=tk.LEFT, padx=(10, 0))

        # 物品列表
        self.items_listbox = tk.Listbox(detection_frame, height=4)
        self.items_listbox.pack(fill=tk.X, pady=(10, 0))
        self.items_listbox.bind("<Double-Button-1>", self.on_item_selected)

        # 实时预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="实时预览", padding="10")
        preview_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 预览控制
        preview_control_frame = ttk.Frame(preview_frame)
        preview_control_frame.pack(fill=tk.X)

        self.preview_button = ttk.Button(preview_control_frame, text="开始预览",
                                        command=self.toggle_preview)
        self.preview_button.pack(side=tk.LEFT)

        ttk.Button(preview_control_frame, text="截图",
                  command=self.take_screenshot).pack(side=tk.LEFT, padx=(10, 0))

        # 预览画布（缩小版）
        self.preview_canvas = tk.Canvas(preview_frame, width=320, height=240, bg="black")
        self.preview_canvas.pack(pady=(10, 0))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置主框架的行权重
        main_frame.rowconfigure(6, weight=1)

        # 初始化预览状态
        self.is_previewing = False
        self.preview_thread = None

        self.log("程序初始化完成")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def toggle_auto_collection(self):
        """切换自动采集状态"""
        if not self.is_running:
            self.start_auto_collection()
        else:
            self.stop_auto_collection()
            
    def start_auto_collection(self):
        """开始自动采集"""
        self.is_running = True
        self.start_button.config(text="停止采集")
        self.status_var.set("运行中")
        self.log("开始自动采集...")
        
        # 在新线程中运行自动采集
        threading.Thread(target=self.auto_collection_loop, daemon=True).start()
        
    def stop_auto_collection(self):
        """停止自动采集"""
        self.is_running = False
        self.start_button.config(text="开始自动采集")
        self.status_var.set("已停止")
        self.log("停止自动采集")
        
    def auto_collection_loop(self):
        """自动采集主循环"""
        while self.is_running:
            try:
                # 捕获游戏画面
                screenshot = self.screen_capture.capture_game_window()
                if screenshot is None:
                    self.log("未找到游戏窗口")
                    time.sleep(1)
                    continue
                
                # 检测物品
                items = self.item_detector.detect_items(screenshot)
                if items:
                    self.log(f"检测到 {len(items)} 个物品")
                    
                    # 选择最近的物品
                    nearest_item = self.find_nearest_item(items)
                    if nearest_item:
                        self.log(f"前往采集物品: {nearest_item['type']}")
                        self.move_to_item(nearest_item)
                
                time.sleep(0.5)  # 控制循环频率
                
            except Exception as e:
                self.log(f"错误: {str(e)}")
                time.sleep(1)
                
    def find_nearest_item(self, items):
        """找到最近的物品"""
        # 这里需要实现距离计算逻辑
        if items:
            return items[0]  # 暂时返回第一个物品
        return None
        
    def move_to_item(self, item):
        """移动到物品位置"""
        # 这里需要实现路径规划和移动逻辑
        target_pos = item.get('position', {})
        if target_pos:
            path = self.path_finder.find_path(self.current_position, target_pos)
            if path:
                self.game_controller.follow_path(path)
                
    def manual_pathfinding(self):
        """手动寻路"""
        try:
            target = {
                "x": float(self.target_x.get()),
                "y": float(self.target_y.get()),
                "z": float(self.target_z.get())
            }
            self.log(f"开始寻路到: {target}")
            
            path = self.path_finder.find_path(self.current_position, target)
            if path:
                self.game_controller.follow_path(path)
                self.log("寻路完成")
            else:
                self.log("无法找到路径")
                
        except ValueError:
            messagebox.showerror("错误", "请输入有效的坐标数值")
            
    def set_target(self):
        """设置目标坐标"""
        try:
            self.target_position = {
                "x": float(self.target_x.get()),
                "y": float(self.target_y.get()),
                "z": float(self.target_z.get())
            }
            self.log(f"目标坐标已设置: {self.target_position}")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的坐标数值")
            
    def open_ai_trainer(self):
        """打开AI训练工具"""
        self.ai_trainer.open_trainer_window()
        
    def on_item_selected(self, event):
        """物品选择事件"""
        selection = self.items_listbox.curselection()
        if selection:
            item_index = selection[0]
            # 这里可以实现选中物品的处理逻辑
            self.log(f"选中物品: {self.items_listbox.get(item_index)}")

    def toggle_preview(self):
        """切换预览状态"""
        if not self.is_previewing:
            self.start_preview()
        else:
            self.stop_preview()

    def start_preview(self):
        """开始实时预览"""
        self.is_previewing = True
        self.preview_button.config(text="停止预览")
        self.log("开始实时预览...")

        # 在新线程中运行预览
        self.preview_thread = threading.Thread(target=self.preview_loop, daemon=True)
        self.preview_thread.start()

    def stop_preview(self):
        """停止实时预览"""
        self.is_previewing = False
        self.preview_button.config(text="开始预览")
        self.log("停止实时预览")

    def preview_loop(self):
        """预览循环"""
        while self.is_previewing:
            try:
                # 捕获游戏画面
                screenshot = self.screen_capture.capture_game_window()
                if screenshot is not None:
                    # 检测物品
                    items = self.item_detector.detect_items(screenshot)

                    # 更新物品列表
                    self.update_items_list(items)

                    # 在预览画布上显示
                    self.update_preview_canvas(screenshot, items)

                time.sleep(0.1)  # 控制预览帧率

            except Exception as e:
                self.log(f"预览错误: {str(e)}")
                time.sleep(1)

    def update_items_list(self, items):
        """更新物品列表"""
        self.items_listbox.delete(0, tk.END)
        self.items_count_var.set(str(len(items)))

        for item in items:
            item_text = f"{item['type']} (置信度: {item['confidence']:.2f})"
            self.items_listbox.insert(tk.END, item_text)

    def update_preview_canvas(self, image, items):
        """更新预览画布"""
        try:
            # 绘制检测结果
            result_image = self.item_detector.draw_detections(image, items)

            # 缩放图像以适应画布
            height, width = result_image.shape[:2]
            canvas_width = 320
            canvas_height = 240

            scale_x = canvas_width / width
            scale_y = canvas_height / height
            scale = min(scale_x, scale_y)

            new_width = int(width * scale)
            new_height = int(height * scale)

            resized_image = cv2.resize(result_image, (new_width, new_height))
            rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)

            # 转换为PhotoImage
            from PIL import Image, ImageTk
            pil_image = Image.fromarray(rgb_image)
            photo = ImageTk.PhotoImage(pil_image)

            # 更新画布
            self.preview_canvas.delete("all")
            self.preview_canvas.create_image(canvas_width//2, canvas_height//2, image=photo)

            # 保持引用防止垃圾回收
            self.preview_canvas.image = photo

        except Exception as e:
            print(f"预览更新错误: {e}")

    def take_screenshot(self):
        """截图功能"""
        screenshot = self.screen_capture.capture_game_window()
        if screenshot is not None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"

            # 创建screenshots目录
            os.makedirs("screenshots", exist_ok=True)
            filepath = os.path.join("screenshots", filename)

            cv2.imwrite(filepath, screenshot)
            self.log(f"截图已保存: {filename}")
        else:
            self.log("截图失败：未找到游戏窗口")

    def open_settings(self):
        """打开设置窗口"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("500x400")
        settings_window.resizable(False, False)

        # 设置窗口内容
        main_frame = ttk.Frame(settings_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 游戏设置
        game_frame = ttk.LabelFrame(main_frame, text="游戏设置", padding="10")
        game_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(game_frame, text="移动速度:").grid(row=0, column=0, sticky=tk.W)
        move_speed_var = tk.StringVar(value=str(self.config_manager.get("game.movement_speed", 1.0)))
        ttk.Entry(game_frame, textvariable=move_speed_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Label(game_frame, text="转向速度:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        turn_speed_var = tk.StringVar(value=str(self.config_manager.get("game.turn_speed", 1.0)))
        ttk.Entry(game_frame, textvariable=turn_speed_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # 检测设置
        detection_frame = ttk.LabelFrame(main_frame, text="检测设置", padding="10")
        detection_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(detection_frame, text="置信度阈值:").grid(row=0, column=0, sticky=tk.W)
        confidence_var = tk.StringVar(value=str(self.config_manager.get("detection.confidence_threshold", 0.5)))
        ttk.Entry(detection_frame, textvariable=confidence_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 保存按钮
        def save_settings():
            try:
                self.config_manager.set("game.movement_speed", float(move_speed_var.get()))
                self.config_manager.set("game.turn_speed", float(turn_speed_var.get()))
                self.config_manager.set("detection.confidence_threshold", float(confidence_var.get()))
                self.config_manager.save_config()

                # 更新组件设置
                self.game_controller.move_speed = float(move_speed_var.get())
                self.game_controller.turn_speed = float(turn_speed_var.get())
                self.item_detector.set_confidence_threshold(float(confidence_var.get()))

                messagebox.showinfo("成功", "设置已保存")
                settings_window.destroy()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数值")

        ttk.Button(main_frame, text="保存设置", command=save_settings).pack(pady=(20, 0))

    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("程序被用户中断")
        finally:
            # 清理资源
            if self.is_running:
                self.stop_auto_collection()
            if self.is_previewing:
                self.stop_preview()

if __name__ == "__main__":
    app = YanYunAutoBot()
    app.run()
