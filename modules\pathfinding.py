"""
路径规划模块
实现A*寻路算法和3D路径规划
"""

import math
import heapq
import numpy as np
from typing import List, Dict, Tuple, Optional, Set
import json
import os

class Node:
    def __init__(self, x: float, y: float, z: float = 0):
        self.x = x
        self.y = y
        self.z = z
        self.g_cost = 0  # 从起点到当前节点的实际代价
        self.h_cost = 0  # 从当前节点到终点的启发式代价
        self.f_cost = 0  # 总代价 f = g + h
        self.parent = None
        self.walkable = True
    
    def __lt__(self, other):
        return self.f_cost < other.f_cost
    
    def __eq__(self, other):
        return (abs(self.x - other.x) < 0.1 and 
                abs(self.y - other.y) < 0.1 and 
                abs(self.z - other.z) < 0.1)
    
    def __hash__(self):
        return hash((round(self.x, 1), round(self.y, 1), round(self.z, 1)))
    
    def to_dict(self):
        return {"x": self.x, "y": self.y, "z": self.z}

class PathFinder:
    def __init__(self):
        self.grid_size = 1.0  # 网格大小
        self.obstacles = set()  # 障碍物集合
        self.map_bounds = {
            "min_x": -1000, "max_x": 1000,
            "min_y": -1000, "max_y": 1000,
            "min_z": 0, "max_z": 100
        }
        self.movement_cost = {
            "horizontal": 1.0,
            "vertical": 1.4,  # 对角线移动代价更高
            "climb": 2.0      # 垂直移动代价最高
        }
        
        # 加载地图数据
        self.load_map_data()
    
    def load_map_data(self, map_file: str = "data/map_data.json"):
        """加载地图数据"""
        try:
            if os.path.exists(map_file):
                with open(map_file, 'r', encoding='utf-8') as f:
                    map_data = json.load(f)
                    
                # 加载障碍物
                if "obstacles" in map_data:
                    for obs in map_data["obstacles"]:
                        self.add_obstacle(obs["x"], obs["y"], obs.get("z", 0))
                
                # 加载地图边界
                if "bounds" in map_data:
                    self.map_bounds.update(map_data["bounds"])
                    
                print(f"地图数据已加载: {len(self.obstacles)} 个障碍物")
            else:
                print("地图文件不存在，使用默认设置")
                self.create_default_obstacles()
                
        except Exception as e:
            print(f"加载地图数据失败: {e}")
            self.create_default_obstacles()
    
    def create_default_obstacles(self):
        """创建默认障碍物（用于测试）"""
        # 添加一些示例障碍物
        for i in range(-50, 51, 10):
            for j in range(-50, 51, 10):
                if abs(i) > 20 or abs(j) > 20:
                    self.add_obstacle(i, j, 0)
    
    def add_obstacle(self, x: float, y: float, z: float = 0):
        """添加障碍物"""
        node = Node(x, y, z)
        node.walkable = False
        self.obstacles.add((round(x, 1), round(y, 1), round(z, 1)))
    
    def remove_obstacle(self, x: float, y: float, z: float = 0):
        """移除障碍物"""
        key = (round(x, 1), round(y, 1), round(z, 1))
        if key in self.obstacles:
            self.obstacles.remove(key)
    
    def is_walkable(self, x: float, y: float, z: float = 0) -> bool:
        """检查位置是否可行走"""
        # 检查是否在地图边界内
        if (x < self.map_bounds["min_x"] or x > self.map_bounds["max_x"] or
            y < self.map_bounds["min_y"] or y > self.map_bounds["max_y"] or
            z < self.map_bounds["min_z"] or z > self.map_bounds["max_z"]):
            return False
        
        # 检查是否是障碍物
        key = (round(x, 1), round(y, 1), round(z, 1))
        return key not in self.obstacles
    
    def get_neighbors(self, node: Node) -> List[Node]:
        """获取节点的邻居"""
        neighbors = []
        
        # 8个水平方向 + 上下移动
        directions = [
            (1, 0, 0), (-1, 0, 0), (0, 1, 0), (0, -1, 0),  # 四个基本方向
            (1, 1, 0), (1, -1, 0), (-1, 1, 0), (-1, -1, 0),  # 四个对角线方向
            (0, 0, 1), (0, 0, -1)  # 上下方向
        ]
        
        for dx, dy, dz in directions:
            new_x = node.x + dx * self.grid_size
            new_y = node.y + dy * self.grid_size
            new_z = node.z + dz * self.grid_size
            
            if self.is_walkable(new_x, new_y, new_z):
                neighbor = Node(new_x, new_y, new_z)
                neighbors.append(neighbor)
        
        return neighbors
    
    def calculate_distance(self, node1: Node, node2: Node) -> float:
        """计算两个节点之间的距离"""
        dx = node2.x - node1.x
        dy = node2.y - node1.y
        dz = node2.z - node1.z
        
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def calculate_movement_cost(self, from_node: Node, to_node: Node) -> float:
        """计算移动代价"""
        dx = abs(to_node.x - from_node.x)
        dy = abs(to_node.y - from_node.y)
        dz = abs(to_node.z - from_node.z)
        
        # 垂直移动
        if dz > 0:
            return self.movement_cost["climb"]
        # 对角线移动
        elif dx > 0 and dy > 0:
            return self.movement_cost["vertical"]
        # 水平移动
        else:
            return self.movement_cost["horizontal"]
    
    def heuristic(self, node: Node, goal: Node) -> float:
        """启发式函数（曼哈顿距离 + 高度差）"""
        dx = abs(goal.x - node.x)
        dy = abs(goal.y - node.y)
        dz = abs(goal.z - node.z)
        
        # 使用曼哈顿距离作为启发式函数
        return dx + dy + dz * 2  # 垂直移动代价更高
    
    def find_path(self, start_pos: Dict, goal_pos: Dict) -> Optional[List[Dict]]:
        """使用A*算法寻找路径"""
        start_node = Node(start_pos["x"], start_pos["y"], start_pos.get("z", 0))
        goal_node = Node(goal_pos["x"], goal_pos["y"], goal_pos.get("z", 0))
        
        # 检查起点和终点是否可行走
        if not self.is_walkable(start_node.x, start_node.y, start_node.z):
            print("起点不可行走")
            return None
        
        if not self.is_walkable(goal_node.x, goal_node.y, goal_node.z):
            print("终点不可行走")
            return None
        
        # A*算法
        open_set = []
        closed_set = set()
        
        start_node.g_cost = 0
        start_node.h_cost = self.heuristic(start_node, goal_node)
        start_node.f_cost = start_node.g_cost + start_node.h_cost
        
        heapq.heappush(open_set, start_node)
        
        max_iterations = 10000  # 防止无限循环
        iterations = 0
        
        while open_set and iterations < max_iterations:
            iterations += 1
            current_node = heapq.heappop(open_set)
            
            # 到达目标
            if current_node == goal_node:
                path = self.reconstruct_path(current_node)
                print(f"找到路径，长度: {len(path)}, 迭代次数: {iterations}")
                return path
            
            closed_set.add((round(current_node.x, 1), round(current_node.y, 1), round(current_node.z, 1)))
            
            # 检查邻居
            for neighbor in self.get_neighbors(current_node):
                neighbor_key = (round(neighbor.x, 1), round(neighbor.y, 1), round(neighbor.z, 1))
                
                if neighbor_key in closed_set:
                    continue
                
                tentative_g_cost = current_node.g_cost + self.calculate_movement_cost(current_node, neighbor)
                
                # 检查是否已在开放集合中
                in_open_set = False
                for open_node in open_set:
                    if (round(open_node.x, 1) == neighbor_key[0] and 
                        round(open_node.y, 1) == neighbor_key[1] and 
                        round(open_node.z, 1) == neighbor_key[2]):
                        if tentative_g_cost < open_node.g_cost:
                            open_node.g_cost = tentative_g_cost
                            open_node.f_cost = open_node.g_cost + open_node.h_cost
                            open_node.parent = current_node
                            heapq.heapify(open_set)
                        in_open_set = True
                        break
                
                if not in_open_set:
                    neighbor.g_cost = tentative_g_cost
                    neighbor.h_cost = self.heuristic(neighbor, goal_node)
                    neighbor.f_cost = neighbor.g_cost + neighbor.h_cost
                    neighbor.parent = current_node
                    heapq.heappush(open_set, neighbor)
        
        print(f"未找到路径，迭代次数: {iterations}")
        return None
    
    def reconstruct_path(self, goal_node: Node) -> List[Dict]:
        """重构路径"""
        path = []
        current = goal_node
        
        while current is not None:
            path.append(current.to_dict())
            current = current.parent
        
        path.reverse()
        return path
    
    def smooth_path(self, path: List[Dict]) -> List[Dict]:
        """平滑路径，移除不必要的路径点"""
        if len(path) <= 2:
            return path
        
        smoothed_path = [path[0]]
        
        i = 0
        while i < len(path) - 1:
            j = len(path) - 1
            
            # 从最远的点开始检查是否可以直接到达
            while j > i + 1:
                if self.is_line_walkable(path[i], path[j]):
                    smoothed_path.append(path[j])
                    i = j
                    break
                j -= 1
            else:
                # 如果没有找到可直达的远点，就移动到下一个点
                i += 1
                if i < len(path):
                    smoothed_path.append(path[i])
        
        return smoothed_path
    
    def is_line_walkable(self, start: Dict, end: Dict) -> bool:
        """检查两点之间的直线是否可行走"""
        steps = int(self.calculate_distance(Node(**start), Node(**end)) / self.grid_size)
        
        if steps == 0:
            return True
        
        dx = (end["x"] - start["x"]) / steps
        dy = (end["y"] - start["y"]) / steps
        dz = (end.get("z", 0) - start.get("z", 0)) / steps
        
        for i in range(steps + 1):
            x = start["x"] + dx * i
            y = start["y"] + dy * i
            z = start.get("z", 0) + dz * i
            
            if not self.is_walkable(x, y, z):
                return False
        
        return True
    
    def find_nearest_walkable_position(self, target_pos: Dict, search_radius: float = 10.0) -> Optional[Dict]:
        """找到目标位置附近最近的可行走位置"""
        target_x, target_y, target_z = target_pos["x"], target_pos["y"], target_pos.get("z", 0)
        
        # 如果目标位置本身可行走，直接返回
        if self.is_walkable(target_x, target_y, target_z):
            return target_pos
        
        # 螺旋搜索最近的可行走位置
        for radius in np.arange(self.grid_size, search_radius, self.grid_size):
            for angle in np.arange(0, 2 * math.pi, math.pi / 8):
                x = target_x + radius * math.cos(angle)
                y = target_y + radius * math.sin(angle)
                
                if self.is_walkable(x, y, target_z):
                    return {"x": x, "y": y, "z": target_z}
        
        return None
    
    def save_map_data(self, filename: str = "data/map_data.json"):
        """保存地图数据"""
        os.makedirs("data", exist_ok=True)
        
        map_data = {
            "bounds": self.map_bounds,
            "obstacles": [
                {"x": obs[0], "y": obs[1], "z": obs[2]} 
                for obs in self.obstacles
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(map_data, f, ensure_ascii=False, indent=2)
        
        print(f"地图数据已保存: {filename}")

# 测试代码
if __name__ == "__main__":
    pathfinder = PathFinder()
    
    # 测试寻路
    start = {"x": 0, "y": 0, "z": 0}
    goal = {"x": 10, "y": 10, "z": 0}
    
    print(f"寻路测试: 从 {start} 到 {goal}")
    path = pathfinder.find_path(start, goal)
    
    if path:
        print(f"找到路径，包含 {len(path)} 个路径点:")
        for i, point in enumerate(path):
            print(f"  {i+1}: {point}")
        
        # 平滑路径
        smoothed_path = pathfinder.smooth_path(path)
        print(f"平滑后路径，包含 {len(smoothed_path)} 个路径点:")
        for i, point in enumerate(smoothed_path):
            print(f"  {i+1}: {point}")
    else:
        print("未找到路径")
    
    # 保存地图数据
    pathfinder.save_map_data()
