# 燕云十六声自动寻路采集程序

一个基于AI的游戏自动化工具，支持自动寻路、物品识别和采集功能。

## 功能特性

### 🎯 核心功能
- **自动寻路**: 基于A*算法的3D路径规划
- **物品识别**: 深度学习AI模型识别游戏中的可采集物品
- **自动采集**: 智能移动到物品位置并自动采集
- **实时预览**: 实时显示游戏画面和检测结果
- **AI训练**: 内置数据标注和模型训练工具

### 🛠️ 技术特性
- **多线程处理**: 确保UI响应性和性能
- **模块化设计**: 易于扩展和维护
- **配置管理**: 灵活的参数配置系统
- **错误处理**: 完善的异常处理和日志记录

## 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.8或更高
- **内存**: 建议4GB以上
- **显卡**: 支持CUDA的显卡（可选，用于AI加速）

## 安装说明

### 1. 快速安装
```bash
# 克隆或下载项目文件
# 运行安装脚本
python install.py
```

### 2. 手动安装
```bash
# 安装依赖
pip install -r requirements.txt

# 创建必要目录
mkdir models data screenshots training_data
```

## 使用指南

### 1. 启动程序
```bash
# 使用启动脚本（推荐）
python run.py

# 或直接运行主程序
python main.py
```

### 2. 首次使用设置

1. **游戏设置**
   - 确保游戏窗口标题包含"燕云十六声"
   - 建议使用窗口模式运行游戏
   - 调整游戏画面亮度和对比度

2. **AI模型训练**
   - 点击"AI训练工具"按钮
   - 使用标注工具标记游戏中的物品
   - 训练自定义识别模型

3. **参数调整**
   - 点击"设置"按钮调整各项参数
   - 根据游戏情况调整移动速度和检测阈值

### 3. 基本操作

#### 自动采集模式
1. 点击"开始自动采集"
2. 程序会自动检测物品并移动采集
3. 点击"停止采集"结束自动模式

#### 手动寻路模式
1. 在目标坐标框中输入XYZ坐标
2. 点击"设置目标"
3. 点击"手动寻路"开始移动

#### 实时预览
1. 点击"开始预览"查看实时检测结果
2. 预览窗口会显示检测到的物品
3. 可以截图保存当前画面

## 目录结构

```
燕云十六声自动寻路采集程序/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── install.py              # 安装脚本
├── requirements.txt        # 依赖列表
├── config.json            # 配置文件
├── README.md              # 说明文档
├── modules/               # 功能模块
│   ├── __init__.py
│   ├── screen_capture.py  # 屏幕捕获
│   ├── game_controller.py # 游戏控制
│   ├── item_detector.py   # 物品检测
│   ├── pathfinding.py     # 路径规划
│   ├── ai_trainer.py      # AI训练
│   └── config_manager.py  # 配置管理
├── models/                # AI模型文件
├── data/                  # 地图数据
├── screenshots/           # 截图文件
└── training_data/         # 训练数据
    ├── images/           # 训练图像
    └── labels/           # 标注文件
```

## AI训练指南

### 1. 数据收集
- 在游戏中截取包含各种物品的图像
- 确保图像清晰，物品特征明显
- 收集不同光照和角度的样本

### 2. 数据标注
- 使用内置标注工具标记物品位置
- 为每个物品选择正确的类别
- 确保标注框准确包围物品

### 3. 模型训练
- 设置合适的训练参数
- 监控训练进度和损失值
- 测试训练好的模型效果

### 4. 模型部署
- 将训练好的模型保存到models目录
- 在主程序中加载新模型
- 调整检测参数优化效果

## 配置说明

### 游戏配置
```json
{
  "game": {
    "window_title": "燕云十六声",    // 游戏窗口标题
    "movement_speed": 1.0,          // 移动速度
    "turn_speed": 1.0               // 转向速度
  }
}
```

### 检测配置
```json
{
  "detection": {
    "confidence_threshold": 0.5,    // 置信度阈值
    "max_detection_distance": 100.0 // 最大检测距离
  }
}
```

### 寻路配置
```json
{
  "pathfinding": {
    "grid_size": 1.0,              // 网格大小
    "precision_threshold": 5.0      // 到达精度
  }
}
```

## 常见问题

### Q: 程序无法检测到游戏窗口
A: 确保游戏窗口标题包含"燕云十六声"，或在配置文件中修改window_title

### Q: 物品识别准确率低
A: 使用AI训练工具收集更多训练数据，或调整置信度阈值

### Q: 寻路经常卡住
A: 检查地图数据是否正确，或调整网格大小和移动参数

### Q: 程序运行缓慢
A: 降低捕获帧率，关闭不必要的预览功能，使用GPU加速

## 注意事项

⚠️ **重要提醒**
- 本程序仅供学习和研究使用
- 请遵守游戏服务条款和相关法律法规
- 使用自动化工具可能存在封号风险
- 建议在测试环境中使用

## 技术支持

如果遇到问题或需要帮助：
1. 查看运行日志中的错误信息
2. 检查配置文件是否正确
3. 确认所有依赖包已正确安装
4. 尝试重新训练AI模型

## 更新日志

### v1.0.0
- 初始版本发布
- 基础的自动寻路和物品采集功能
- AI训练工具
- 图形用户界面

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。
