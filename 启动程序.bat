@echo off
chcp 65001 >nul
title 燕云十六声自动寻路采集程序

echo ================================================
echo 燕云十六声自动寻路采集程序
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 启动程序...
python run.py

echo.
echo 程序已退出
pause
