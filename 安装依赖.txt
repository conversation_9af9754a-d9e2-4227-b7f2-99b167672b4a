@echo off
chcp 65001 >nul
title 燕云十六声自动寻路采集程序 - 安装向导

echo ================================================
echo 燕云十六声自动寻路采集程序 - 安装向导
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 开始安装依赖...
python install.py

echo.
echo 安装完成！
echo 现在可以运行"启动程序.bat"来启动程序
pause
